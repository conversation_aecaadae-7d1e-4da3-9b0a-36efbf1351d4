{% extends "layouts/base.html" %}
{% load i18n static %}
{% load crispy_forms_tags %}
{% load utils %}
{% block stylesheets %}
  <link rel="stylesheet" href="{{ STATIC_URL }}assets/css/plugins/style.css"/>
  {% comment %}
	<link rel="stylesheet" href="{% static 'assets/css/plugins/dropzone.min.css' %}" /> {% endcomment %}
  {% comment %} <link rel="stylesheet" href="{{ STATIC_URL }}assets/cdns_locals/css/dropzone/dropzone.min-v5.css" type="text/css"/> {% endcomment %}
  <link rel="stylesheet" crossorigin href="{{ STATIC_URL }}assets\cdns_locals\css\all\v6.2.1\fontawesome-all.css"/>
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery.min-v3.6.0.js"></script>

  <style>
    .table .row {
      padding: 5px 0px;
      border-bottom: 1px solid #ddd;
    }

    .error-list-item {
      color: black;
      font-weight: bold;
      font-size: 1.2em;
    }

    .adjustment390 {
      color: red;
      font-weight: bold;
      font-size: 5em;
    }

  </style>
{% endblock stylesheets %}
{% block title %}{% trans "Resumen Usuario"%}{% endblock title %}
{% block breadcrumb %}
  <div class="page-header">
    <div class="page-block">
      <div class="row align-items-center">
        <div class="col-md-12">
          <div class="page-header-title">
            <h5 class="m-b-10">
              <a href="javascript:history.back()"><i class="feather icon-arrow-left"></i></a> &nbsp;
              {% trans "Resumen Usuario"%}
            </h5>
          </div>
          <ul class="breadcrumb">
            <li class="breadcrumb-item">
              <a href="{% url 'home' %}"><i class="feather icon-home"></i></a>
            </li>
            <li class="breadcrumb-item">
              <a href="{% url 'app_sellers:list' %}">{% trans "Vendedores"%}</a>
            </li>
            <li class="breadcrumb-item">
              <a href="{% url 'app_sellers:summary' seller.shortname %}"> {{ seller.name.capitalize }} </a>
            </li>
            <li class="breadcrumb-item">
              <a href=".">
                {% if model_id is not None %}
                  {% trans "Modelo"%} {{ model_id }}
                {% else %}
                  {% trans "Modelos"%}
                {% endif %}
              </a>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
{% endblock breadcrumb %}
{% block content %}
  <div class="mt-3">
    <div class="row justify-content-end">
      <div class="col-2  mt-3">
        <label class="form-label" for="period-input">{% trans "Periodo"%}:</label>
        <select class="form-select form-control" name="period-input" id="period-input" onchange="onchangef()">
          {% if anual_model %}
            <option value="0A">{% trans "Anual"%}</option>
          {% elif model_id == "202" %}
            <option value="M4">{% trans "1P - (abril)"%}</option>
            <option value="M10">{% trans "2P - (octubre)"%}</option>
            <option value="M12">{% trans "3P - (diciembre)"%}</option>
          {% else %}
            <option value="Q1">{% trans "Trimestre 1"%}</option>
            <option value="Q2">{% trans "Trimestre 2"%}</option>
            <option value="Q3">{% trans "Trimestre 3"%}</option>
            <option value="Q4">{% trans "Trimestre 4"%}</option>
          {% endif %}
        </select>
      </div>
      <div class="col-2  mt-3">
        <label class="form-label" for="year-input">{% trans "Año"%}:</label>
        <select class="form-select form-control" name="year-input" id="year-input" onchange="onchangef()">
          <option value="2022">2022</option>
          <option value="2023">2023</option>
          <option value="2024">2024</option>
          <option value="2025">2025</option>
        </select>
      </div>
    </div>
    <br>
    <div class="card-header">
      {% comment %}
				<form action="{% url 'hijack:acquire' %}" method="POST">
					{% csrf_token %}
					<input type="hidden" name="user_pk" value="{{ seller.user.id }}">
					<input type="hidden" name="next" value="{{ request.path }}">
					<h2>
						<button type="submit" class="btn btn-outline-link m-0 p-0">
							<h2><i class="feather icon-user"></i>
								{{seller.name.capitalize}}
								<i class="feather icon-external-link"></i>
							</h2>
						</button>
					</h2>
				</form>
			{% endcomment %}
      <h2>{{ seller.name.capitalize }}</h2>
    </div>
    <div class="card-body" style="min-height: 68vh;">
      <div class="row d-flex">
        <div class="col-12 m-2">
          <div class="row">
            <!-- alerta visualizacion no chrome -->
            <div id="browser-warning" class="alert alert-warning" style="display:none;"></div>
            <div class="col">
                <!-- Añadir div para mostrar carga y errores -->
                <div id="loading-pdf" style="display: none; text-align: center; padding: 20px;">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Cargando...</span>
                    </div>
                    <p>Cargando PDF, por favor espere...</p>
                </div>
                <div id="pdf-error-message" class="alert alert-danger" style="display: none;"></div>
                  <embed id="pdf"
                        style="width: 100%; height: 100vh;"
                        type="application/pdf"
                        class="pdf border"
                        src="about:blank">
                  </embed> 

                {% if model_id == '349' or model_id == '349DB' %}
                    <br> <p><b> Tabla 349 Removida por quedarse en desuso. Chequear lo valores de la tabla del PDF que esta mas actualizado. </b></p> <br>
                {% endif %}
                {% if model_id == '369' %}
                    <br> <p><b> Tabla 369 Removida por quedarse en desuso. Chequear lo valores de la tabla del PDF que esta mas actualizado. </b></p> <br>
                {% endif %}
            </div>
            <div>
              <form method="post" enctype="multipart/form-data" id="submitForm"
                    action="{% url 'app_documents:presented_model_create' seller.shortname %}">
                {% csrf_token %}
                <input type="file" name="file" id="file" style="display:none;"/>
                <input
                  type="hidden"
                  id="id"
                  name="{{ form_create.seller.name }}"
                  value="{{ seller.pk }}"
                />
                <input
                  type="hidden"
                  id="country"
                  name="country"
                  value="{{ model_country }}"
                />
                <input
                  type="hidden"
                  id="model"
                  name="model"
                  value="{{ model_country }}-{{ model_id }}"
                />
                <input
                  type="hidden"
                  id="period"
                  name="period"
                />
                <input
                  type="hidden"
                  id="year"
                  name="year"
                />
                <input
                  type="hidden"
                  id="status"
                  name="status"
                  value="pending"
                />
                <input 
                type="hidden" 
                id="modelNotEmail" 
                name="modelNotEmail" 
                />
                <input 
                  type="hidden" 
                  id="modelValidation" 
                  name="modelValidation"
                />

                <input type="hidden" class="form-control" id="send_to_external_manager" value="false" name="send_to_external_manager">
                <br>
                <!-- Modal Trigger-->
                <div class="d-flex justify-content-center">
                  <!--
                  <button type="button" id="regenratepdfButton" class="btn btn-secondary" onclick="regenerate_pdf()">
                    <b>
                      <i class="fa-solid fa-rotate-right"></i>
                      &nbsp; Volver a Generar PDF &nbsp; 
                      <i class="fa-solid fa-xl fa-file-pdf"></i>
                    </b>
                  </button>
                  -->

                  {% if model_id not in fax_models%}
                    <button type="button" id="pendingButtonNotEmail" data-bs-toggle="modal" data-bs-target="#modalNotEmail"
                            class="btn " style="background-color: #E67E22; color: white;" disabled>
                      <b>
                        <i class="fa-regular fa-xl fa-square-check"></i>
                        &nbsp; {% trans "Generar SIN ENVIAR EMAIL"%} &nbsp;
                      </b>
                    </button>
                  {% else %}
                    <button type="button" id="pendingButtonNotEmail" data-bs-toggle="modal" data-bs-target="#modalNotEmail"
                          class="btn btn-warning" disabled>
                    <b>
                      <i class="fa-regular fa-xl fa-square-check"></i>
                      &nbsp; {% trans "Enviar Fax"%} &nbsp;
                    </b>
                  </button>
                  {% endif %}
                  <button type="button" id="pendingButton" data-bs-toggle="modal" data-bs-target="#modal"
                        class="btn btn-warning div_buttons" disabled>
                  <b>
                    <i class="fa-regular fa-xl fa-square-check"></i>
                    &nbsp; {% trans "Mandar a Revisión"%} &nbsp;
                  </b>
                  </button>

                  {% if model_id == 'LIPE' %}
                    <button type="button" class="btn btn-info" id="downloadButton" onclick="generate_xml()">
                      <b>
                        <i class="fa-solid fa-download"></i>
                        &nbsp; {% trans "Descargar XML"%} &nbsp;
                        <i class="fa-regular fa-file"></i>
                      </b>
                    </button>
                  {% else %}
                    <button type="button" class="btn btn-info div_buttons d-none" id="downloadButton" onclick="generate_txt()">
                      <b>
                        <i class="fa-solid fa-download "></i>
                        &nbsp; {% trans "Descargar TXT"%} &nbsp;
                        <i class="fa-regular fa-file"></i>
                      </b>
                    </button>
                    <button type="button" 
                            class="btn btn-primary div_buttons d-none" 
                            id="buttonSendAEAT"
                            data-bs-toggle="modal" 
                            data-bs-target="#modalSendAEAT">
                      <b>
                        <i class="fa-solid fa-up-right-from-square"></i>
                        &nbsp; {% trans "Presentar en la AEAT"%} &nbsp;
                        <i class="fa-regular fa-file"></i>
                      </b>
                    </button>
                  {% endif %}

                  {% if model_id == '347' %}
                    <button type="button" class="btn btn-info" id="downloadCartButton" onclick="downloadCartMod347()">
                      <b>
                        <i class="fa-solid fa-download"></i>
                        &nbsp; {% trans "Descargar Cartas"%} &nbsp;
                        <i class="fa-regular fa-file"></i>
                      </b>
                    </button>
                    <button type="button" class="btn btn-info" id="sendEmailButton">
                      <b>
                        <i class="fa-regular fa-paper-plane"></i>
                        &nbsp; {% trans "Enviar e-mail explicativo"%}&nbsp;
                      </b>
                    </button>
                  {% endif %}

                  {% if model_id in excel_calc_models %}
                    <button type="button" class="btn btn-info div_buttons" id="downloadExcel" onclick="downloadExcelModel()">
                      <b>
                        <i class="fa-solid fa-download "></i>
                        &nbsp; Cálculos Excel &nbsp;
                        <i class="fa-regular fa-file"></i>
                      </b>
                    </button>
                  {% endif %}
                </div>

                <div class="d-flex justify-content-center">
                  <footer id="footer" style="color:red; display:none;">
                    <b>* {% trans "Ya existe un modelo generado para este periodo y mandado al cliente"%}.</b>
                  </footer>
                  <footer id="footer-pdf" style="color:orange; display:none;"></footer>
                </div>

                <!-- >> MODAL SECTION << -->

                <!-- Modal Enviar a revisión -->
                  {% include 'sellers/include/modals_send_model_revision/modal_send_revision.html'  with hidden=False%}
                <!-- Modal Enviar a revisión -->

                <!-- Modal Enviar a revisión sin enviar el email -->
                  {% include 'sellers/include/modals_send_model_revision/peding_model_not_email.html'  with hidden=False%}
                <!-- Modal Enviar a revisión sin enviar el email -->

                <!-- Modal Errores de al generar TXT -->
                  {% include 'sellers/include/modals_send_model_revision/modal_error_txt.html'  with hidden=False%}
                <!-- Modal Errores de al generar TXT -->

              <!-- >> MODAL SECTION << -->
              </form>

              <div class="justify-content-center mt-2" id='force-status' style="display:none;">
                <form method="post" enctype="multipart/form-data" action="{% url 'app_documents:presented_model_forced_create' seller.shortname %}" id='form-forced'>
                  {% csrf_token %}
                  <button type="button" id="force-required" class="btn div_buttons" style="background-color: #535659; color: #CBE7C2;" onclick="submitFormForced('required')" disabled>
                    <b>
                      <i class="fa-regular fa-xl fa-square-check"></i>
                      &nbsp; {% trans "FORZAR REQUERIDO"%} &nbsp;
                    </b>
                  </button>

                  <button type="button" id="force-not-required" class="btn div_buttons" style="background-color: #535659; color: #FF6466;" onclick="submitFormForced('not-required')" disabled>
                    <b>
                      <i class="fa-regular fa-xl fa-square-check"></i>
                      &nbsp; {% trans "FORZAR NO REQUERIDO"%} &nbsp;
                    </b>
                  </button>
                  <input
                    type="hidden"
                    id="country-force"
                    name="country"
                    value="{{ model_country }}"
                  />
                  <input
                    type="hidden"
                    id="model-force"
                    name="model"
                    value="{{ model_country }}-{{ model_id }}"
                  />
                  <input
                    type="hidden"
                    id="period-force"
                    name="period"
                  />
                  <input
                    type="hidden"
                    id="year-force"
                    name="year"
                  />
                  <input
                    type="hidden"
                    id="status-force"
                    name="status"
                    value="not-required"
                  />
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
{% endblock content %}
{% block javascripts %}
  <!-- sweet alert Js -->
  <script src="{% static 'assets/js/plugins/sweetalert2.all.min.js' %}"></script>
  <script>
    // Variable de depuración
    const debug = true;

    function detectBrowser() {
      let userAgent = navigator.userAgent.toLowerCase();
      debug && console.log('User Agent: ' + userAgent); // Log para verificar el user agent
      
      if (userAgent.indexOf('edg') > -1) {
        return 'edge';
      } else if (userAgent.indexOf('opr') > -1) {
        return 'opera';
      } else if (userAgent.indexOf('chrome') > -1 && userAgent.indexOf('safari') > -1) {
        return 'chrome';
      } else if (userAgent.indexOf('firefox') > -1) {
        return 'firefox';
      } else if (userAgent.indexOf('safari') > -1 && userAgent.indexOf('chrome') === -1) {
        return 'safari';
      } else if (userAgent.indexOf('msie') > -1 || userAgent.indexOf('trident') > -1) {
        return 'ie';
      } else {
        return 'other';
      }
    }

    let file_date = null;
    const sellerObj = JSON.parse("{{ seller_json|escapejs }}")[0].fields;
    const csrfToken = '{{ csrf_token }}';
    let isPdfLoading = false; // Bandera para controlar cargas concurrentes
    let detectedBrowser = 'other'; // Variable global para el navegador
    let chromeLoadTimer = null; // Temporizador para Chrome
    let embed = document.getElementById('pdf');

    console.log("empieza la carga del PDF");

    //Función que hace una llamada al backend para calcular el modelo y establecer el objeto PDF en el embed
    //** EVITAR LLAMADAS CONCURRENTES AL BACKEND DE ESTA FUNCIÓN, PROVOCA ERRORES Y DISCORDANCIA DE DATOS**
    const onchangef = async (regenerate = true) => {

      Swal.fire({
          title: 'Generando PDF',
          text: 'Por favor, espere mientras se genera el PDF.',
          timerProgressBar: true,
          didOpen: () => Swal.showLoading(),
          showCancelButton: false,
          showConfirmButton: false,
          allowOutsideClick: false,
          allowEscapeKey: false,
          allowEnterKey: false
      });

      // Limpiar temporizador existente si hay alguno
      if (chromeLoadTimer) {
        clearTimeout(chromeLoadTimer);
        chromeLoadTimer = null;
      }

      isPdfLoading = true; // Indicar que la carga ha comenzado
      disableButtons(); // Deshabilitar botones mientras se carga 

      const modelId = "{{ model_id }}";

      // Get Period and Year
      let period = document.getElementById('period-input');
      let year = document.getElementById('year-input');
      document.getElementById('year').value = year.value;
      document.getElementById('year-force').value = year.value;
      document.getElementById('period').value = period.value;
      document.getElementById('period-force').value = period.value;

      // Get PDF	
      
      if (!embed) {
        console.error("Error: No se encontró el elemento PDF para el modelo " + modelId);
        Swal.fire({
          icon: 'error',
          title: 'Error al cargar el PDF',
          text: 'No se pudo encontrar el elemento para mostrar el PDF del modelo ' + modelId + '. Por favor, contacte con soporte técnico.',
          confirmButtonText: 'Entendido'
        });
        isPdfLoading = false; // Resetear bandera si hay error crítico
        return;
      }


      // Primero verificamos si se requiere firma para este modelo
      if (modelId === 'FR-MANDATO' || modelId === 'mandato_francia' || modelId === 'FR-CONTRATO' || modelId === 'CONTRATOFRA') {
          await checkSignatureFirst(period.value, year.value, regenerate);
      } else {
        urlembed = "{% url 'app_sellers:model_update_pdf' seller.shortname model_id %}";
        newUrl = urlembed + '?period=' + period.value + '&year=' + year.value;
        if (regenerate == true) {
          newUrl = newUrl + '&regenerate=true';
        }

        // Añadir listeners ANTES de establecer src
        embed.removeEventListener('load', onLoadIframe);
        embed.removeEventListener('error', handleEmbedError);

        console.log("isPdfLoading: " + isPdfLoading);

        // Para Chrome, establecemos un temporizador de seguridad adicional
        if (detectedBrowser === 'chrome') {
          // Timer de seguridad para Chrome: forzará la visualización después de 1.5 segundos
          chromeLoadTimer = setTimeout(() => {
            if (isPdfLoading) {  // Solo si aún está en carga
              onLoadIframe();  // Llamar manualmente a onLoadIframe
            }
          }, 1500);
        }

        // Añadir event listeners
        embed.addEventListener('load', onLoadIframe, { once: true });
        embed.addEventListener('error', handleEmbedError, { once: true });

        // Llamada al back para calcular el modelo y obtener el PDF
        try {
            const response = await fetch(newUrl, {
              method: 'GET',
              headers: {
                'X-Requested-With': 'XMLHttpRequest', 
              },
            });
            if (!response.ok) {
              Swal.fire({
                icon: 'error',
                title: 'Error al cargar el PDF',
                text: 'No se pudo cargar el PDF del modelo ' + modelId + '. Por favor, intente nuevamente más tarde.',
              });
            }

            const blob = await response.blob();
            const blobUrl = URL.createObjectURL(blob);
            embed.src = blobUrl;

            //Esteblecer el input file con el PDF descargado
            setPDFUrl(blob,response);

            //Habilitar/deshabilitar botones en función de si existe un modelo generado
            enableDisableButton(period, year); 

            Swal.close(); // Cerrar el modal de carga

          } catch (error) {
            console.error('Error al obtener el PDF:', error);
          }
      }

      if (modelId == '111') {
        toogleGestorExterno(period.value, year.value, sellerObj);
      }

    }

    const onLoadIframe = () => {

      // Limpiar el temporizador de Chrome si existe
      if (chromeLoadTimer) {
        clearTimeout(chromeLoadTimer);
        chromeLoadTimer = null;
      }

      const embed = document.getElementById('pdf');
      const loadingDiv = document.getElementById('loading-pdf');

      // Detectar el navegador y mostrar el mensaje si no es Chrome
      const browser = detectBrowser();
      debug && console.log('Detected Browser: ' + browser); // Log para verificar el navegador detectado
      const warningMessage = document.getElementById('browser-warning');
      if (warningMessage) {
        if (browser !== 'chrome') {
          warningMessage.textContent = `{% trans "Está usando el navegador"%} ${browser}. {% trans "Podría tener problemas en la visualización. Sugerimos que use Chrome"%}.`;
          warningMessage.style.display = 'block';
        } else {
          warningMessage.style.display = 'none';
        }
      }
    }

    const disableButtons = () => {
      document.getElementById('force-status').style.display = "none";
      document.getElementById('force-required').disabled = true;
      document.getElementById('force-not-required').disabled = true;
      document.getElementById('submitButton').disabled = true;
      document.getElementById('pendingButton').disabled = true;
      document.getElementById('submitButtonNotEmail').disabled = true;
      document.getElementById('pendingButtonNotEmail').disabled = true;
      document.getElementById('downloadButton').disabled = true;
      // document.getElementById('regenratepdfButton').disabled = true;				
      document.getElementById('footer').style.display = "none";

      const faxModels = {{ fax_models|safe }};
      
      if (faxModels.includes("{{ model_id }}")){
        let buttonsDivs = document.getElementsByClassName('div_buttons');
        for (let i = 0; i < buttonsDivs.length; i++) {
            buttonsDivs[i].style.display = "none";
        }
      }   

    }

    const toogleGestorExterno = (period, year, sellerObj) => {
      const { periodStartDate, periodEndDate } = periodToDate(period, year);

      const {
          is_contracted_labor_payroll,
          contracted_labor_payroll_date,
          contracted_labor_payroll_end_date,
          is_contracted_corporate_payroll,
          contracted_corporate_payroll_date,
          contracted_corporate_payroll_end_date
      } = sellerObj;

      const payrollLaborableStartDate = contracted_labor_payroll_date ? new Date(contracted_labor_payroll_date) : null;
      const payrollLaborableEndDate = contracted_labor_payroll_end_date ? new Date(contracted_labor_payroll_end_date) : null;
      const corporatePayrollStartDate = contracted_corporate_payroll_date ? new Date(contracted_corporate_payroll_date) : null;
      const corporatePayrollEndDate = contracted_corporate_payroll_end_date ? new Date(contracted_corporate_payroll_end_date) : null;

      const externalEmailCheckInput = document.getElementById('send_to_external_manager');
      const payrollContractedElements = document.querySelectorAll('#payroll_contracted');
      const payrollContractedMessage = document.querySelectorAll('#payroll_contracted_message');

      const isLaborableInPeriod = checkPayrollInPeriod(payrollLaborableStartDate, payrollLaborableEndDate, periodStartDate, periodEndDate);
      const isCorporatePayrollInPeriod = checkPayrollInPeriod(corporatePayrollStartDate, corporatePayrollEndDate, periodStartDate, periodEndDate);

      if (isLaborableInPeriod) {
        payrollContractedElements.forEach(element => element.classList.remove('d-none'));
        if (!isCorporatePayrollInPeriod) {
            payrollContractedMessage.forEach(element => {
                element.innerHTML = `
                    {% trans "Este vendedor/seller tiene contratado <b>Nóminas Laborales</b> para este período, 
                    es decir que la presentación de este modelo es gestionada por un gestor externo.
                    Por lo tanto, una vez que el vendedor esté conforme con el modelo, 
                    este se enviará automáticamente al gestor externo para su revisión y posterior presentación."%}
                `;
            });
            externalEmailCheckInput.value = "true";
        } else {
            payrollContractedMessage.forEach(element => {
                element.innerHTML = `
                    {% trans "Este vendedor/seller tiene contratado <b>Nóminas Laborales</b> y <b>Nóminas Societarias</b> para este período.
                    Ten esto en cuenta al momento de hacer la presentación."%}
                `;
            });
            externalEmailCheckInput.value = "false";
        }
      } else {
          payrollContractedElements.forEach(element => element.classList.add('d-none'));
          externalEmailCheckInput.value = "false";
      }
    }

    const checkPayrollInPeriod = (nominaStartDate, nominaEndDate, periodStartDate, periodEndDate) => {
      if (!nominaStartDate) return false;

      if (!nominaEndDate) {
          return nominaStartDate <= periodEndDate;
      }

      return nominaStartDate <= periodEndDate && nominaEndDate >= periodStartDate;
    };

    const enableDisableButton = (period = null, year = null) => {
      if (period == null) {
        period = document.getElementById('period-input');
      }
      if (year == null) {
        year = document.getElementById('year-input');
      }
      let array_periods = {{array_periods|safe}};
      let periods_input = year.value + period.value;
      const modelId = "{{ model_id }}";

      let exist_model = array_periods.includes(periods_input);

      if (exist_model == true) {
        document.getElementById('force-status').style.display = "none";
        document.getElementById('force-required').disabled = true;
        document.getElementById('force-not-required').disabled = true;
        document.getElementById('pendingButton').disabled = true;
        document.getElementById('pendingButtonNotEmail').disabled = true;
        document.getElementById('downloadButton').disabled = false;
        document.getElementById('footer').style.display = "block";
        document.getElementById('footer-pdf').style.display = "none";
        document.getElementById('footer-pdf').innerHTML = "";

      } 
      else {
        document.getElementById('force-status').style.display = "flex";
        document.getElementById('force-required').disabled = false;
        document.getElementById('force-not-required').disabled = false;
        document.getElementById('pendingButton').disabled = false;
        document.getElementById('pendingButtonNotEmail').disabled = false;
        document.getElementById('downloadButton').disabled = true;
        document.getElementById('submitButton').disabled = false;;
        document.getElementById('submitButtonNotEmail').disabled = false;;
        document.getElementById('footer').style.display = "none";
      }

      // Lógica específica para modelo 347 y estado email-sent-347
      if (modelId === '347') {
        const emailSendendElement = document.getElementById('email_sendend_data');
        let email_sendend_model347_list = emailSendendElement ? JSON.parse(emailSendendElement.textContent) : [];

        if (email_sendend_model347_list.length > 0) {
          const currentYear = new Date().getFullYear();
          const isEmailSentForPeriod = email_sendend_model347_list.some(entry =>
              entry.year == (currentYear - 1) && entry.period__code === '0A'
          );

          if (isEmailSentForPeriod && period.value === '0A' && year.value == (currentYear - 1)) {
            document.getElementById('pendingButton').disabled = true;
            document.getElementById('pendingButtonNotEmail').disabled = true;
            const divModelExists = document.getElementById('model-already-exists');
            if (divModelExists) {
                divModelExists.style.display = 'block';
                divModelExists.innerHTML = `<i class="ti ti-alert-triangle text-warning"></i> Modelo 347 ya revisado y enviado al cliente para ${year.value}.`;
            }
          }
        }
      }

    }

    const setDefaultPeriodYear = () => {

      const url = new URL(window.location.href);
      const periodParam = url.searchParams.get("period");
      const yearParam = url.searchParams.get("year");

      if (periodParam && yearParam) {
        document.getElementById('period-input').value = periodParam;
        document.getElementById('year-input').value = yearParam;
        if (periodParam == "0A") {
            document.getElementById('period-input').setAttribute("disabled", "disabled");
        }
        onchangef();
        return
      }

      let period = document.getElementById('period-input');
      let year = document.getElementById('year-input');
      let date = new Date();
      let currentYear = date.getFullYear();
      let currentMonth = date.getMonth() + 1;
      let currentDay = date.getDate();
      let anualModel = '{{anual_model|safe}}';
      let model = '{{model_id}}';


      year.value = currentYear;
      if (anualModel == 'True') {
        period.value = "0A";
        period.setAttribute("disabled", "disabled");
        if (currentMonth >= 1 && currentMonth <= 2) {
          year.value = currentYear -1
        }
      } else {
        if (currentMonth >= 2 && currentMonth <= 4) {
          period.value = "Q1";
        }
        if (currentMonth >= 5 && currentMonth <= 7) {
          period.value = "Q2";
        }
        if (currentMonth >= 8 && currentMonth <= 10) {
          period.value = "Q3";
        }
        if ((currentMonth >= 11 && currentMonth <= 12) || currentMonth == 1 ) {
          period.value = "Q4";
          if (currentMonth == 1 ) {
            year.value = currentYear -1
          }
        }

        if (model == "202"){
          if (currentMonth == 4) {
            period.value = "M4";
          }else if (currentMonth == 10 ) {
            period.value = "M10";
          }else if (currentMonth == 12 ) {
            period.value = "M12";
          }else{
            period.value = "M4";
          }
        }
        
      }
      document.getElementById('year').value = currentYear;
      document.getElementById('period').value = period.value;
      onchangef();
    }

    const periodToDate = (period, year) => {
      let periodStartDate, periodEndDate;

      switch (period) {
          case 'Q1':
              periodStartDate = new Date(year, 0, 1);
              periodEndDate = new Date(year, 2, 31);
              break;
          case 'Q2':
              periodStartDate = new Date(year, 3, 1);
              periodEndDate = new Date(year, 5, 30);
              break;
          case 'Q3':
              periodStartDate = new Date(year, 6, 1);
              periodEndDate = new Date(year, 8, 30);
              break;
          case 'Q4':
              periodStartDate = new Date(year, 9, 1);
              periodEndDate = new Date(year, 11, 31);
              break;
          case '0A':
              periodStartDate = new Date(year, 0, 1);
              periodEndDate = new Date(year, 11, 31);
              break;
          default:
              throw new Error('Invalid period');
      }

      return { periodStartDate, periodEndDate };
    };

    //Función para poblar el input file con el PDF del embed
    const setPDFUrl =(blob, response)=> {
      const fileInput = document.getElementById('file');
      const period = document.getElementById('period-input');
      const year = document.getElementById('year-input');

      if ("{{model_country}}" == "IT") {
        filename = "{{seller.shortname}}-IT-" + year.value + "-" + period.value + "-{{model_id}}.pdf"
      } else if("{{model_country}}" == "US"){
        if("{{model_id}}" == "5472"){
          filename = "{{seller.shortname}}-US-" + year.value + "-" + period.value + "-{{model_id}}-1120.pdf"
        }else{
          filename = "{{seller.shortname}}-US-" + year.value + "-" + period.value + "-{{model_id}}.pdf"
        }
      } else if("{{model_country}}" == "FR"){
        filename = "{{seller.shortname}}-FR-" + year.value + "-" + period.value + "-{{model_id}}.pdf"
      } else {
        filename = "{{seller.shortname}}-ES-" + year.value + "-" + period.value + "-{{model_id}}.pdf"
      }
      const lastModified = response.headers.get('last-modified');
      const pdfFile = new File([blob], filename, {
        type: 'application/pdf',
        lastModified: new Date(lastModified)
      });

      const dataTransfer = new DataTransfer();
      dataTransfer.items.add(pdfFile);

      fileInput.files = dataTransfer.files;
    }

    async function generate_txt() {
      
        const seller_id = '{{seller.id}}';
        const period = document.getElementById("period-input").value;
        const year = document.getElementById("year-input").value;
        const model = '{{model_id}}';
        const modal_error = document.getElementById("modal_errors");

        const url = '/docs/presented_models/generate_txt/' + seller_id + '/' + period + '/' + year + '/' + model;
        
      try {
        const response = await fetch(url);

        if (response.status == 200){ 
          let href_url = await response.json();
          href_url = href_url.replace('muaytax', '');
          const downloadLink = document.createElement('a');
          downloadLink.href = href_url;
          downloadLink.setAttribute('download', '');
          downloadLink.click();
        } else if (response.status === 400) {
          const errorArray = await response.json();
          const errorList = document.getElementById("error_list");
          errorList.innerHTML = '';

          for (let i = 0; i < errorArray.length; i++) {
            const li = document.createElement('li');
            li.innerHTML = errorArray[i].error;
            li.classList.add('error-list-item');
            errorList.appendChild(li);
            const br = document.createElement('br');
            errorList.appendChild(br);
          }

          $(modal_error).modal('show');
        } else {
          alert('Error en la solicitud.');
        }
      } catch (error) {
        console.error('Error:', error);
      }
    };

    async function generate_xml() {
      const shortname = '{{seller.shortname}}';
      let seller_id = '{{seller.id}}',
      period = document.getElementById("period-input").value,
      year = document.getElementById("year-input").value,
      model = '{{model_id}}';

      const Toast = Swal.mixin({
        toast: true,
        position: 'top-end',
        showConfirmButton: false,
        timer: 3000,
        timerProgressBar: true,
        // stop timer when hover
        didOpen: (toast) => {
          toast.addEventListener('mouseenter', Swal.stopTimer)
          toast.addEventListener('mouseleave', Swal.resumeTimer)
        }
      });

      var url = `/docs/presented_models/generate_xml/${seller_id}/${period}/${year}/${model}/`;

      var downloadButton = document.getElementById('downloadButton');
      downloadButton.disabled = true;
      downloadButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>&nbsp; {% trans "Descargando xml"%}...';

      try{
        const response = await fetch(url, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/xml',
            'X-CSRFToken': '{{ csrf_token }}'
          }
        });

        if (response.status == 200) {
          const disposition = response.headers.get('Content-Disposition');
          const file_name = disposition.split('filename=')[1].replace(/"/g, ''); // Remove surrounding quotes
          const blob = await response.blob();
          const blobUrl = URL.createObjectURL(blob);
          const link = document.createElement('a');

          link.href = blobUrl;
          link.download = `${file_name}.xml`;
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);

          Toast.fire({
            icon: 'success',
            title: '{% trans "XML descargado correctamente"%}'
          })
        } else if (response.status == 204) {
          Toast.fire({
            icon: 'warning',
            title: '{% trans "No se puede generar XML, aún no se ha mandado a revisión el modelo"%}.'
          })
        } else {
          Toast.fire({
            icon: 'error',
            title: '{% trans "Error al descargar el XML. Intenta más tarde"%}'
          })
        }

      }catch(error){
        Toast.fire({
          icon: 'error',
          title: error
        })
      }
      downloadButton.disabled = false;
      downloadButton.innerHTML = '<b><i class="fa-solid fa-download"></i>&nbsp; {% trans "Descargar XML"%} &nbsp; <i class="fa-regular fa-file"></i></b>';
    }

    // async function to AEAT
    async function sendToAEAT () {
      const seller_id = '{{seller.id}}';
      const period = document.getElementById("period-input").value;
      const year = document.getElementById("year-input").value;
      const model = '{{model_id}}';
      let url = `/docs/presented_models/directAEAT/${seller_id}/${period}/${year}/${model}`;
      try {
          const response = await fetch(url);
          const data = await response.json();

          if ('respuesta' in data) {
            console.log("La clave 'respuesta' está presente en el JSON.");
          }
      }catch(error){
          console.error('Error:', error);
      }
    }

    const downloadExcelModel  = async () => {
      let excelBtn = document.getElementById('downloadExcel');
      excelBtn.disabled = true;
      // Show loading message
      Swal.fire({
        toast: true,
        title: 'Generando Excel...',
        text: 'Por favor, espere un momento.',
        icon: 'info',
        position: 'top-end',
        showConfirmButton: false,
        timerProgressBar: true,
        didOpen: () => {
            Swal.showLoading()
        },
        allowOutsideClick: false, 
        allowEscapeKey: false, 
        allowEnterKey: false 
      });

      const Toast = Swal.mixin({
        toast: true,
        position: 'top-end',
        showConfirmButton: false,
        timer: 3000,
        timerProgressBar: true,
        // stop timer when hover
        didOpen: (toast) => {
          toast.addEventListener('mouseenter', Swal.stopTimer)
          toast.addEventListener('mouseleave', Swal.resumeTimer)
        }
      });
      data = {
        model_id: '{{model_id}}',
        seller: '{{seller.shortname}}',
        year: document.getElementById("year-input").value,
        period: document.getElementById("period-input").value
      };

      const url = `/${data.seller}/model/downloadExcelModel?model_id=${'{{model_id}}'}&year=${data.year}&period=${data.period}`;
      try {
        const response = await fetch(url, {
          method: 'GET',
        });

        if (response.status === 200) {
          const blob = await response.blob();
          const blobUrl = URL.createObjectURL(blob);
          const link = document.createElement('a');
          link.href = blobUrl;
          link.download = `Modelo_${'{{model_id}}'}_${data.period}_${data.year}_Excel_${'{{seller.shortname}}'}.xlsx`;
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          excelBtn.disabled = false;
          Toast.fire({
            icon: 'success',
            title: 'Excel descargado correctamente'
          })
        } else {
          console.error('Error al descargar el archivo Excel:', response.statusText);
          excelBtn.disabled = false;
          Toast.fire({
            icon: 'error',
            title: 'Error al descargar el archivo Excel. Contacta con soporte'
          })
        }
      } catch (error) {
        console.error('Error:', error);
        Toast.fire({
          icon: 'error',
          title: 'Error al descargar el archivo Excel. Contacta con soporte'
        })
      }
    }

    // simple function to link to url app_sellers:model_347_carta using async and await
    async function downloadCartMod347() {
      var shortname = '{{seller.shortname}}';
      var year = document.getElementById("year-input").value;

      // Construct the URL based on the pattern you provided
      var url = `/sellers/${shortname}/carts-mod347/${year}/`;

      var downloadButton = document.getElementById('downloadCartButton');
      downloadButton.disabled = true;
      downloadButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>&nbsp; {% trans "Descargando cartas"%}...';

      const Toast = Swal.mixin({
        toast: true,
        position: 'top-end',
        showConfirmButton: false,
        timer: 3000,
        timerProgressBar: true,
        didOpen: (toast) => {
          toast.addEventListener('mouseenter', Swal.stopTimer)
          toast.addEventListener('mouseleave', Swal.resumeTimer)
        }
      });

      try {
        const response = await fetch(url, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/zip',
            'X-CSRFToken': '{{ csrf_token }}'
          }
        });

        if (response.status === 200) {
            const blob = await response.blob();
            const blobUrl = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = blobUrl;
            link.download = `Cartas_modelo347_${shortname}.zip`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            Toast.fire({
              icon: 'success',
              title: '{% trans "Cartas descargadas correctamente"%}'
            })
        } else {
          Toast.fire({
            icon: 'error',
            title: '{% trans "Error al descargar las cartas. Intenta más tarde"%}'
          })
        }

      } catch (error) {
        Toast.fire({
          icon: 'error',
          title: '{% trans "Ha habido un error con el servidor. Comunícate con el departamento de IT"%}: ' + error
        })
      }
      downloadButton.disabled = false;
      downloadButton.innerHTML = '<b><i class="fa-solid fa-download"></i>&nbsp; {% trans "Descargar Cartas"%} &nbsp; <i class="fa-regular fa-file"></i></b>';
    }

    // sends notification email usando fetch
    const sendNotificationButton = document.getElementById('sendEmailButton');
    if (sendNotificationButton) {
      sendNotificationButton.addEventListener('click', function () {
        var shortname = '{{seller.shortname}}';
        var year = document.getElementById("year-input").value;

        Swal.fire({
          // icon: "success",
          title: '{% trans "Enviando notificación"%}',
          text: '{% trans "Espera unos segundos. No recargues la página"%}',
          timer: 5000,
          timerProgressBar: true,
          allowEscapeKey: false,
          allowEnterKey: false,
          allowOutsideClick: false,
          didOpen: () => {
            Swal.showLoading()
          },
        }).then((result) => {
          if (result.dismiss === Swal.DismissReason.timer) {
            console.log('I was closed by the timer')
          }
        })

        fetch(`/sellers/${shortname}/send-notification-model-347/${year}/`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': '{{ csrf_token }}'
          }
        })
          .then(response => response.json())
          .then(data => {
            Swal.hideLoading();
            if (data.status === 200) {
              const Toast = Swal.mixin({
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 3000,
                timerProgressBar: true,
                didOpen: (toast) => {
                  toast.addEventListener('mouseenter', Swal.stopTimer)
                  toast.addEventListener('mouseleave', Swal.resumeTimer)
                }
              });
              Toast.fire({
                icon: 'success',
                title: data.message
              })
            } else if (data.status === 204) {
              const Toast = Swal.mixin({
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 3500,
                timerProgressBar: true,
                didOpen: (toast) => {
                  toast.addEventListener('mouseenter', Swal.stopTimer)
                  toast.addEventListener('mouseleave', Swal.resumeTimer)
                }
              });
              Toast.fire({
                icon: 'warning',
                title: '{% trans "No se ha enviado email. Vendedor no tiene clientes/proveedores que cumplan la condición del modelo 347"%}'
              })
            } else {
              const Toast = Swal.mixin({
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 2500,
                timerProgressBar: true,
                didOpen: (toast) => {
                  toast.addEventListener('mouseenter', Swal.stopTimer)
                  toast.addEventListener('mouseleave', Swal.resumeTimer)
                }
              });
              Toast.fire({
                icon: 'error',
                title: '{% trans "Error al enviar el email. Intenta más tarde"%}'
              })
            }
          })
          .catch(error => {
            console.error('Error:', error);
            const Toast = Swal.mixin({
              toast: true,
              position: 'top-end',
              showConfirmButton: false,
              timer: 2500,
              timerProgressBar: true,
              didOpen: (toast) => {
                toast.addEventListener('mouseenter', Swal.stopTimer)
                toast.addEventListener('mouseleave', Swal.resumeTimer)
              }
            });
            Toast.fire({
              icon: 'error',
              title: '{% trans "No se ha podido conectar con el servidor. Comunícate con el departamento de IT"%}'
            })
          });
      });
    }
    
    // get adjustment info and validations from models and show it in modal
    const infoModelSendRevision = async (prevData)=>{
      const shortname = '{{seller.shortname}}';
      const modelId = '{{model_id}}';
      let period = document.getElementById("period-input").value;
      let year = document.getElementById("year-input").value;
      let warning = document.getElementById("warning_model");

      warning.innerHTML = '';

      const bodyData = {
        period: period,
        year: year,
        model: modelId,
        prevData: prevData
      };
      
      try {
          const response = await fetch(`/${shortname}/model/infoModelSendRevision/`, {
            method: 'POST',
            headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': '{{ csrf_token }}'
            },
            body: JSON.stringify(bodyData)
          });
          const data = await response.json();
          
          if (response.status == 200){
            if (modelId == '390' && prevData){
              let span = document.createElement('span');
              span.innerHTML =`
                            <p><h1 class=" text-center"><b> Datos:</b></h1></p>
                            <p class="text-center" style="color:black; font-size: 20px;" ><b> CA 86 {% trans "(sin ajuste)"%}: ${data.ca086_old} </b></p>
                            <p class="text-center" style="color:black; font-size: 20px;" ><b> {% trans "VALOR absoluto de"%}: (CA097 + CA098 + CA662 - CA095) = ${data.first_sum} </b></p>
                            <p class="text-center" style="color:black; font-size: 20px;" ><b> CA 86 {% trans "(CON ajuste)"%}: ${data.ca086} </b></p>
                            <p class="text-center" style="color:black; font-size: 20px;" ><b> CA 606 {% trans "(sin ajuste)"%}: ${data.ca606_old} </b></p>
                            <p><h4 class=" text-center" style="color:red;" ><b> {% trans "Se ha realizado un reajuste a la casilla 606 de"%}:</b></h4></p>
                            <p><h4 class=" text-center adjustment390" style="color:red;" ><b> ${data.adjustment}</b></h4></p>
                            <p><h4 class=" text-center" style="color:red;" ><b>{% trans "Correspondiente a un margen de error del"%}:</b></h4></p>
                            <p><h4 class=" text-center adjustment390" style="color:red;" ><b> ${data.percentage}</b></h4></p>
                            <p><h4 class=" text-center" style="color:red;" ><b>{% trans "Si el ajuste es mayor de lo esperado, no envíes el modelo a revisión y comprueba que los valores que tenemos en la APP son correctos"%}</b></h4></p>`

              document.getElementById("warning_model").appendChild(span);
            }
            return response.status;

          } else if (response.status === 400 && Array.isArray(data) && data.length > 0 && 'error' in data[0]){
            errorNotification(data);
            return response.status;
          }
      } catch (error) {
          Swal.fire({
            icon: 'error',
            title: 'Error',
            text: '{% trans "Error contacta con soporte"%}.'
        });
        return null;
      }
      
    }

    const submitFormForced = (status) => {
      document.getElementById('status-force').value = status;
      form = document.getElementById('form-forced');
      form.submit();
    }

    // modal wait for the fax to be sent
    const modal_wait_fax = () =>{
      const faxModels = {{ fax_models|safe }}
      if (faxModels.includes("{{ model_id }}")){
        $('#modalNotEmail').modal('hide');
        Swal.fire({
          title: 'Enviando fax...',
          text: '{% trans "Puede demorar unos minutos, tenga paciencia"%}.',
          timerProgressBar: true,
          didOpen: () => Swal.showLoading(),
          showCancelButton: false,
          showConfirmButton: false,
          allowOutsideClick: false,
          allowEscapeKey: false,
          allowEnterKey: false
        });
      }
    }

    //Sweet alert error
    const errorNotification = (data) => {
      if (Array.isArray(data) && data.length > 0 && 'error' in data[0]) {
        let erroresHTML = data.map(function(errorObj) {
            return '<div style="text-align: left;"><li>' + errorObj.error + '</li></div><br>';
        }).join('');
        Swal.fire({
          icon: 'error',
          title: '{% trans "Se han encontrado los siguientes errores"%}:',
          width: 1200,
          html: erroresHTML
        });
      }else{
        Swal.fire({
            icon: 'error',
            title: 'Error',
            text: '{% trans "Error contacta con soporte"%}.'
        });
      }
    }


    // Aseguramos que submitFormForced, modal_wait_fax y errorNotification estén
    // disponibles globalmente para llamadas desde HTML
    window.submitFormForced = submitFormForced;
    window.modal_wait_fax = modal_wait_fax;
    window.errorNotification = errorNotification;
    window.generate_txt = generate_txt;
    window.generate_xml = generate_xml;
    window.downloadExcelModel = downloadExcelModel;
    window.downloadCartMod347 = downloadCartMod347;

    // ---- START EXECUTION LOGIC (runs after DOM is ready) ----
    $(document).ready(function () {

      // 0. Detectar navegador
      detectedBrowser = detectBrowser();

      // 1. Establecer los valores de período y año, esta función llama en su interior a onchangef (establece el PDF en el embed y en el input file)
      // ** IMPORTANTE: DURANTE LA CARGA INICIAL NO LLAMAR A "onchangef" explicitamente ya que la función hace crea el PDF en el BACKEND y las diferentes llamadas pueden pisarse entre sí**
      setDefaultPeriodYear();

      // +++ Add initial call for model 390 +++
      let modelId = "{{ model_id }}";
      if (modelId === '390') {
        infoModelSendRevision(true);
      }
      // --- End initial call ---

      // Add/remove value when notEmail modal show/hide
      $('#modalNotEmail').on('shown.bs.modal', function () {
        console.log("Modal notEmail shown");
        $('#modelNotEmail').val('notEmail')
      });
      $('#modal').on('shown.bs.modal', function () {
        $('#modelNotEmail').val('')
      });

      // Add/remove validation value when you want to send the model without validation
      $('#NotValidation').click(function() {
          $('#modelValidation').val('notValidation')
      });
      $('#NotValidationNotEmail').click(function() {
          $('#modelValidation').val('notValidation')
      });

      // Add/remove validation value when you want to send the model with validation
      $('#submitButton').click(function() {
          $('#modelValidation').val('')
      });
      $('#submitButtonNotEmail').click(function() {
          $('#modelValidation').val('')
      });

      // Event listener for form submission
      document.getElementById("submitForm").addEventListener("submit", async function(event) {
          event.preventDefault();
          validation = document.getElementById('modelValidation').value;
          const isFaxModel = {{ fax_models|safe }}.includes("{{ model_id }}"); // Check if it's a fax model

          if (validation == 'notValidation'){
            $('#modal').modal('hide');
            $('#modalNotEmail').modal('hide');
            Swal.fire({
                title: isFaxModel ? 'Enviando fax...' : 'Generando modelo...',
                text: isFaxModel ? 'Puede demorar unos minutos, tenga paciencia.' : 'Por favor espere, no cierres ni recargues la página.',
                timerProgressBar: true,
                didOpen: () => Swal.showLoading(),
                showCancelButton: false,
                showConfirmButton: false,
                allowOutsideClick: false,
                allowEscapeKey: false,
                allowEnterKey: false
            });
            this.submit();
          }else{
            $('#modal').modal('hide');
            $('#modalNotEmail').modal('hide');
            Swal.fire({
                title: isFaxModel ? 'Validando y enviando fax...' : 'Validando y generando modelo...',
                text: isFaxModel ? 'Puede demorar unos minutos, tenga paciencia.' : 'Por favor espere, no cierres ni recargues la página.',
                timerProgressBar: true,
                didOpen: () => Swal.showLoading(),
                showCancelButton: false,
                showConfirmButton: false,
                allowOutsideClick: false,
                allowEscapeKey: false,
                allowEnterKey: false
            });
            // Esperamos a que la promesa se resuelva y obtenemos el status.
            const status = await infoModelSendRevision(false);

            if (status == 200) {
                this.submit();
            }
          }
      });

    });
    // ---- END EXECUTION LOGIC ----

    // Función para manejar errores de carga del embed (cuando el navegador no puede cargar el recurso)
    function handleEmbedError(event) {
      console.error("handleEmbedError: Error al cargar el src del embed:", event.target.src);
      isPdfLoading = false; // Resetear bandera
      // Este error es a nivel del navegador, no necesariamente una respuesta HTTP 400
      handlePdfError("El navegador no pudo cargar el recurso PDF. Verifique la URL o la conexión.");
    }

    // Función centralizada para mostrar mensajes de error y limpiar el estado
    function showErrorMessage(message) {
        const errorDiv = document.getElementById('pdf-error-message');
        const embed = document.getElementById('pdf');
        const loadingDiv = document.getElementById('loading-pdf');

        if (errorDiv) {
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
        } else {
            // Fallback si no existe el div de error
            alert("Error: " + message);
        }

        // Ocultar PDF y loading
        if (embed) embed.style.display = 'none';
        if (loadingDiv) loadingDiv.style.display = 'none';

        // Limpiar src para evitar intentos fallidos repetidos
        if (embed) embed.src = 'about:blank';

        // Deshabilitar botones de acción si es necesario
        disableButtons();
    }

    // Función para manejar errores genéricos o específicos de carga del PDF
    function handlePdfError(errorMessage = "Error al cargar el PDF") {
      console.error("handlePdfError:", errorMessage);
      isPdfLoading = false; // Asegurarse que la bandera de carga se resetee

      // Mostrar mensaje de error usando la función centralizada
      showErrorMessage(errorMessage);

      // Podrías querer limpiar la URL del embed o hacer otras acciones de limpieza aquí
      const embed = document.getElementById('pdf');
      if (embed) {
        embed.src = 'about:blank'; // Limpiar para evitar mostrar un PDF roto o anterior
        embed.style.display = 'none'; // Ocultar el embed
      }

      // Asegurar que la animación de carga se oculte
      const loadingDiv = document.getElementById('loading-pdf');
      if (loadingDiv) {
        loadingDiv.style.display = 'none';
      }

      // Deshabilitar botones relevantes
      disableButtons();
    }

    // Función para verificar primero si hay firma y manejar errores JSON en 400
    async function checkSignatureFirst(period, year, regenerate = true) {
      const embed = document.getElementById('pdf');
      const urlembed = "{% url 'app_sellers:model_update_pdf' seller.shortname model_id %}";
      let checkUrl = urlembed + '?period=' + period + '&year=' + year;
      const modelId = "{{ model_id }}"; // Asegúrate que model_id esté disponible

      // Añadir regenerate=true solo si es necesario y el modelo lo permite
      if (regenerate) {
        checkUrl += '&regenerate=true';
      }

      try {
        const response = await fetch(checkUrl);
        const responseClone = response.clone(); // Clonar para poder leer el cuerpo varias veces si es necesario

        if (!response.ok) {
          console.error(`checkSignatureFirst: Fetch fallido con estado ${response.status}`);
          let errorMessage = `Error ${response.status}`; // Mensaje por defecto
          try {
            // Intentar parsear como JSON PRIMERO
            const errorData = await responseClone.json(); // Usar el clon aquí
            if (errorData && errorData.message) {
              errorMessage = errorData.message; // Usar el mensaje del JSON si existe
            } else {
              // Si no hay JSON o mensaje, intentar leer como texto (puede ser HTML de error 500)
              errorMessage = `Error ${response.status}: La respuesta del servidor no contiene un mensaje de error específico.`
              console.warn("checkSignatureFirst: La respuesta de error no es JSON o no tiene 'message'.", await response.text());
            }
          } catch (parseError) {
             console.warn("checkSignatureFirst: No se pudo parsear error como JSON.");
             // Fallback si no se pudo parsear como JSON
             errorMessage = `Error ${response.status}: No se pudo interpretar la respuesta del servidor.`;
          }
          // Mostrar el error (sea del JSON o texto)
          handlePdfError(errorMessage);
          isPdfLoading = false; // Resetear bandera en caso de error
        } else {
          // Si la respuesta es correcta, cargamos el PDF en el embed

          // Añadir listeners ANTES de establecer src
          embed.removeEventListener('load', onLoadIframe);
          embed.removeEventListener('error', handleEmbedError);

          // Para Chrome, establecemos un temporizador de seguridad adicional
          if (detectedBrowser === 'chrome') {
            // Timer de seguridad para Chrome: forzará la visualización después de 1.5 segundos
            chromeLoadTimer = setTimeout(() => {
              if (isPdfLoading) {  // Solo si aún está en carga
                onLoadIframe();  // Llamar manualmente a onLoadIframe
              }
            }, 1500);
          }

          // Añadir event listeners
          embed.addEventListener('load', onLoadIframe, { once: true });
          embed.addEventListener('error', handleEmbedError, { once: true });

          // Establecer la fuente del PDF
          embed.src = checkUrl;
        }
      } catch (error) {
        console.error('Error en checkSignatureFirst fetch:', error);
        handlePdfError(`Error de red al verificar documento: ${error.message}`);
        isPdfLoading = false; // Resetear bandera en caso de error de red
      }
    }


  </script>
  {{ array_periods | json_script:"array_periods_data" }}
  {{ email_sendend_model347 | json_script:"email_sendend_data" }}
  {{ fax_models | json_script:"fax_models_data" }}
  {{ excel_calc_models | json_script:"excel_calc_models_data" }}
{% endblock javascripts %}