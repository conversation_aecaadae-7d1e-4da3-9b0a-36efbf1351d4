{% extends "layouts/base.html" %}
{% load i18n static crispy_forms_tags utils %}
{% block stylesheets %}
  <link rel="stylesheet" crossorigin href="{{ STATIC_URL }}assets/css/plugins/style.css"/>
  <link rel="stylesheet" crossorigin href="{{ STATIC_URL }}assets/css/loading.css"/>
  <script type="module" src="{{ STATIC_URL }}assets/js/plugins/multi-checkbox.js"></script>
  <link rel="stylesheet" href="{% static 'assets/fonts/material/css/materialdesignicons.min.css' %}">

  <link rel="stylesheet" crossorigin href="{{ STATIC_URL }}assets\cdns_locals\css\all\v6.2.1\fontawesome-all.css" type="text/css"/>

  <link rel="stylesheet" href="{{ STATIC_URL }}assets/cdns_locals/css/dataTables/dataTables.dataTables-v2.0.8.css" />
  <link rel="stylesheet" crossorigin href="{{ STATIC_URL }}assets/cdns_locals/css/dataTables/dataTables.bootstrap5.min-v2.0.8.css" type="text/css"/>

  <link rel="stylesheet" crossorigin href="{{ STATIC_URL }}assets/cdns_locals/css/jquery/jquery.dataTables.min-v1.10.25.css"
        type="text/css"/>
  <link rel="stylesheet" crossorigin href="{{ STATIC_URL }}assets/cdns_locals/css/select/select.dataTables.min-v1.6.2.css"
        type="text/css"/>
  <script src="{% url 'javascript-catalog' %}"></script>
  <style>
    /* styles data table start */
    thead tr th:after, thead tr th:before {
      display: none !important;
    }
    .dropdown-menu.show:before {
      display: none;
    }
    .column-width-limit {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 13vw;
    }
    table.table.dataTable>tbody>tr {
      background-color: #fff!important;
    }
    #invoices-table tbody tr:hover td {
      background: #d6d6d6d2!important;
    }
    table tr td, table tr th {
      vertical-align: middle;
      border: none;
    }
    table.dataTable tbody tr td.select-checkbox:before {
      display: none;
    }

    table.dataTable tbody tr.selected td.select-checkbox:after {
      display: none;
    }

    input#miId,
    input#select_all {
      margin-left: 8px;
      width: 14px;
      height: 14px;
    }
    .table-head {
      top: 0;
      background-color: #f2f2f2;
      z-index: 1;
    }
    /* styles datatable end */


    /* multiselectors style */
    multi-transaction,
    multi-status,
    multi-invoicetype,
    multi-month,
    multi-economicactivity,
    multi-arrivalcountry,
    multi-departurecountry,
    multi-taxcountry,
    multi-vatrates {
      /* Element */
      --mc-z-index: 8 !important;
      --mc-cont-z-index: 20 !important;
      --mc-border: 1px solid #ced4da;;
      --mc-display: inline-block;
      --mc-font: 14px !important;
      --mc-margin: 0;
      --mc-vertical-align: middle;

      /* Dropdown */
      --mc-dropdown-background: #fff;
      --mc-dropdown-box-shadow: 0 1px 2px 0 rgba(57, 70, 92, .05);
      --mc-dropdown-max-height: 350px;
      --mc-dropdown-text-align: left;
      --mc-dropdown-width: 100%;

      /* Input */
      --mc-target-background: #fff;
      --mc-target-color: #000000;
      --mc-target-cursor: pointer;
      --mc-target-margin: 0;
      --mc-target-padding: 0px 0px 0px 3px;
      --mc-target-outline: none;
      --mc-vertical-align: middle;
      --mc-target-width: 268px;

      /* Dropdown Line Items */
      --mc-dropdown-line-height: 2em;
      --mc-ul-margin: 0;
      --mc-ul-padding: 0;
      --mc-checkbox-height: 30px;
      --mc-checkbox-width: 20px;
      --mc-li-cursor: pointer;

      /* Toggle Button */
      --mc-toggle-button-background: #ffffff;
      --mc-toggle-button-color: #000000;
      --mc-toggle-button-cursor: pointer;
      --mc-toggle-button-height: 46px;
      --mc-toggle-button-outline: none;
      --mc-toggle-button-width: 23px;

      /* Close Button */
      --mc-close-button-background: #efefef;
      --mc-close-button-border: none;
      --mc-close-button-border-radius: default;
      --mc-close-button-color: #000000;
      --mc-close-button-cursor: pointer;
      --mc-close-button-display: block;
      --mc-close-button-height: 22px;
      --mc-close-button-margin: 5px auto;
      --mc-close-button-outline: none;
      --mc-close-button-width: 22px;
    }

    #multi-value:hover {
      background-color: #0d6efd;
      --mc-dropdown-color: #fff;
    }

    #multi-value:hover div {
      color: #fff;
    }
    /* multiselectors style */

    /* accordion reset settings */
    .accordion-button:not(.collapsed) {
      color: #111;
      background-color: transparent;
      box-shadow: none;
    }
    .accordion-button:focus {
      z-index: 3;
      border-color: #86b7fe;
      outline: 0;
      box-shadow: none;
    }
    /* accordion reset settings */

    /* table in details */
    #tableColapse1 table tr td:first-child,
    #tableColapse2 table tr td:first-child,
    #tableColapse3 table tr td:first-child{
      min-width: max-content;
    }
    .table-hover tbody tr:hover, .table-striped tbody tr:nth-of-type(odd) {
      background-color: #e8eaed99!important;
    }
    /* table in details */

    .list-action-btn-block{
      position: relative;
    }
    .list-action-btn-block > *{
      font-size: 12px;
      margin-bottom: unset!important;
      margin-right: unset!important;
    }
    .list-action-btn-block button{
      min-width: max-content;
    }
    .dropdown-form {
      display: block;
      min-width: 500px;
      position: absolute;
      background-color: white;
      border: 1px solid #ccc;
      padding: 10px;
      z-index: 1000;
      transform: translate(-70%, 10px);
      opacity: 0;
      transition: opacity 0.3s ease, transform 0.3s ease;
    }
    .btn.dropdown-toggle:after {
      display: none
    }

    /* Estilos para el selector de filas por página */
    .list-action-btn-block select.form-select {
      position: relative;
      padding-right: 2.5rem !important;
      background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e");
      background-repeat: no-repeat;
      background-position: right 0.75rem center;
      background-size: 16px 12px;
      transition: background-image 0.2s ease;
    }

    .list-action-btn-block select.form-select.dropdown-open {
      background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M14 11l-6-6-6 6'/%3e%3c/svg%3e");
    }

    .top-right-badge{
      position: absolute!important;
      top: -10px!important;
      right: -5px!important;
      width: 25px!important;
      height: 25px!important;
      align-items: center;
      display: flex;
      justify-content: center;
    }

    .search-container {
      display: inline-block;
      position: relative;
      width: 200px;
      transition: width 0.4s ease-in-out;
    }

    .search-container > input {
      font-size: 12px;
    }

    .search-container:focus-within {
      width: 100%; /* Expanded width */
    }

  </style>
{% endblock stylesheets %}
{% block title %}
  Facturas
{% endblock title %}
{% block breadcrumb %}
  <div class="page-header">
    <div class="page-block">
      <div class="row align-items-center">
        <div class="col-md-12">
          <div class="page-header-title">
            <h5 class="m-b-10">
              <a href="javascript:history.back()"><i class="feather icon-arrow-left"></i></a> &nbsp;
              {% trans "Facturas: Listado Facturas" %}
            </h5>
          </div>
          <div class="row">
            <div class="col">
              <ul class="breadcrumb">
                <li class="breadcrumb-item">
                  <a href="{% url 'home' %}"><i class="feather icon-home"></i></a>
                </li>
                <li class="breadcrumb-item">
                  <a href=".">{% trans 'Facturas' %}</a>
                </li>
                {% if category %}
                  <li class="breadcrumb-item">
                    <a href="./{{ category.pk }}">{% trans 'Facturas de' %} {{ category }}</a>
                  </li>
                {% endif %}
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
{% endblock breadcrumb %}
{% block content %}
  <div class="row">
    <div class="col-12">
      <div class="card-body">
        <!-- Totals | START -->
        {% if category %}
          {% if category.pk == "sales" or category.pk == "expenses" %}
            <div class="vue">
              <div class="row mt-3 mb-0" style="display: none;" v-show="true">

                <div class="col-md-12 col-lg-6 col-xl-4">
                  <div class="card rounded-3 shadow-none border">
                    <div class="card-body">
                      {% if category.pk == 'sales' %}
                        <h6 class="mt-0">{% trans 'Entradas' %}</h6>
                        <span class="my-2 h3 fw-bold">[[ invoiceDataJSON.total_sales ]] €</span>
                        <i class="fas fa-level-down-alt float-end f-30 text-success"></i>
                        {% if is_self_employed and seller.eqtax == True %}
                          <h6 class="mt-0 mb-3 text-muted">{% trans '(Base + IVA + Req. Eq.)' %}</h6>
                        {% else %}
                          <h6 class="mt-0 mb-3 text-muted">{% trans '(Base + IVA)' %}</h6>
                        {% endif %}
                      {% elif category.pk == 'expenses' %}
                        <h6 class="mt-0">{% trans 'Salidas' %}</h6>
                        <span class="my-2 h3 fw-bold">[[ invoiceDataJSON.total_expenses ]] €</span>
                        <i class="fas fa-level-up-alt float-end f-30 text-danger"></i>
                        {% if is_self_employed and seller.eqtax == True %}
                          {% if seller.is_direct_estimation %}
                            <h6 class="mt-0 mb-3 text-muted" style="min-width: max-content;">{% trans '(Base + IVA + Req. Eq. + Gastos dif.just.)' %}</h6>
                          {% else  %}
                            <h6 class="mt-0 mb-3 text-muted">{% trans '(Base + IVA + Req. Eq.)' %}</h6>
                          {% endif %}
                        {% elif is_self_employed and seller.is_direct_estimation  %}
                          <h6 class="mt-0 mb-3 text-muted">{% trans '(Base + IVA + Gastos dif.just.)' %}</h6>
                        {% else %}
                          <h6 class="mt-0 mb-3 text-muted">{% trans '(Base + IVA)' %}</h6>
                        {% endif %}
                      {% endif %}
                    </div>
                  </div>
                </div>
                <div class="col-md-12 col-lg-6 col-xl-8">
                  <div class="card rounded-3 shadow-none border">
                    <div class="card-body">
                      {% if category.pk == 'sales' %}
                        <table class="table-striped" style="width: 100%;">
                          <tr>
                              <td style="max-width: 100px;">{% trans "Base" %}</td>
                              <td :class="['float-end', getPosNegClass(invoiceDataJSON.total_sales_amount), ]">
                                [[ invoiceDataJSON.total_sales_amount ]] €
                              </td>
                          </tr>
                          <tr>
                              <td style="max-width: 100px;">{% trans "IVA" %}</td>
                              <td :class="['float-end', getPosNegClass(invoiceDataJSON.total_sales_vat), ]">
                                [[ invoiceDataJSON.total_sales_vat ]] €
                              </td>
                          </tr>
                          {% if is_self_employed and seller.eqtax == True %}
                            <tr>
                                <td style="max-width: 100px;">{% trans "Req. Equivalencia" %}</td>
                                <td class="float-end">[[ invoiceDataJSON.total_sales_eqtax ]] €</td>
                            </tr>
                          {% endif %}
                          <tr>
                            <td style="max-width: 100px;">{% trans "IRPF" %}</td>
                            <td :class="['float-end', getPosNegClass(invoiceDataJSON.total_sales_irpf), ]">
                              [[ invoiceDataJSON.total_sales_irpf ]] €
                            </td>
                          </tr>
                          <tr>
                              <td class="fw-bold text-black">{% trans "Total" %}</td>
                              {% if is_self_employed and seller.eqtax == True %}
                              <td class="float-end fw-bold text-black">{% trans "Base + IVA + Req. Eq." %}</td>
                              {% else %}
                              <td class="float-end fw-bold text-black">{% trans "Base + IVA" %}</td>
                              {% endif %}
                          </tr>
                        </table>
                      {% elif category.pk == 'expenses' %}
                        <table class="table-striped" style="width: 100%;">
                          <tr>
                              <td style="max-width: 100px;">{% trans "Base" %}</td>
                              <td :class="['float-end', ]">
                                [[ invoiceDataJSON.total_expenses_amount ]] €
                              </td>
                          </tr>
                          <tr>
                              <td style="max-width: 100px;">{% trans "IVA" %}</td>
                              <td :class="['float-end', ]">
                                [[ invoiceDataJSON.total_expenses_vat ]] €
                              </td>
                          </tr>
                          {% if is_self_employed and seller.eqtax == True %}
                          <tr>
                              <td style="max-width: 100px;">{% trans "Req. Equivalencia" %}</td>
                              <td :class="['float-end',]">
                                [[ invoiceDataJSON.total_expenses_eqtax ]] €
                              </td>
                          </tr>
                          {% endif %}
                          {% if is_self_employed and seller.is_direct_estimation  %}
                          <tr>
                              <td style="max-width: 100px;">
                                {% trans "Gastos dif. just." %} &nbsp;
                                <i
                                  role="button" class="fas fa-info-circle text-muted"
                                  data-bs-toggle="tooltip" data-bs-placement="top"
                                  data-bs-original-title="{% trans 'Gastos dificilmente justificables (5% beneficios)' %}"></i>
                              </td>
                              <td class="float-end">[[ invoiceDataJSON.total_sevent_porcent ]] €</td>
                          </tr>
                          {% endif %}
                          <tr>
                            <td style="max-width: 100px;">{% trans "IRPF" %}</td>
                            <td :class="['float-end', getPosNegClass(invoiceDataJSON.total_expenses_irpf), ]">
                              [[ invoiceDataJSON.total_expenses_irpf ]] €
                            </td>
                          </tr>
                          <tr>
                              <td class="fw-bold text-black">{% trans "Total" %}</td>
                              {% if is_self_employed and seller.eqtax == True %}
                                {% if seller.is_direct_estimation %}
                                  <td class="float-end fw-bold text-black">{% trans "Base + IVA + Req. Eq. + Gastos dif.just." %}</td>
                                {% else  %}
                                  <td class="float-end fw-bold text-black">{% trans "Base + IVA + Req. Eq." %}</td>
                                {% endif %}
                              {% elif is_self_employed and seller.is_direct_estimation  %}
                                <td class="float-end fw-bold text-black">{% trans "Base + IVA + Gastos dif.just." %}</td>
                              {% else %}
                                <td class="float-end fw-bold text-black">{% trans "Base + IVA" %}</td>
                              {% endif %}
                          </tr>
                        </table>
                      {% endif %}
                    </div>
                  </div>
                </div>

              </div>
            </div>
          {% endif %}
        {% else %}
          <div class="vue">
            <div class="row mt-3" v-show="true">

              <div class="col-md-12 col-lg-6 col-xl-4">
                <div class="card rounded-3 shadow-none border">
                  <div class="card-body">
                    <h6 class="mt-0">{% trans "Entradas"%}</h6>
                    <span class="my-2 h3 fw-bold">[[ invoiceDataJSON.total_sales ]] €</span>
                    <i class="fas fa-level-down-alt float-end f-30 text-success"></i>
                  </div>
                  <div class="card-body pt-0">
                    <div class="accordion-item border-0">
                      <h2 class="accordion-header" id="heading3">
                        <button
                          class="accordion-button p-0 collapsed fw-bold" type="button"
                          data-bs-toggle="collapse" data-bs-target="#collapse1"
                          aria-expanded="false" aria-controls="collapse1"
                        >{% trans "Ver detalles" %}</button>
                      </h2>
                      <div id="collapse1" class="accordion-collapse collapse" aria-labelledby="headingOne" data-bs-parent="#accordionExample" style="">
                          <div id="tableColapse1" class="pt-2">
                              <table class="table-striped" style="width: 100%;">
                                  <tr>
                                      <td style="max-width: 100px;">Base</td>
                                      <td :class="['float-end', getPosNegClass(invoiceDataJSON.total_sales_amount), ]">
                                        [[ invoiceDataJSON.total_sales_amount ]] €
                                      </td>
                                  </tr>
                                  <tr>
                                      <td style="max-width: 100px;">IVA</td>
                                      <td :class="['float-end', getPosNegClass(invoiceDataJSON.total_sales_vat), ]">
                                        [[ invoiceDataJSON.total_sales_vat ]] €
                                      </td>
                                  </tr>
                                  {% if is_self_employed and seller.eqtax == True %}
                                  <tr>
                                      <td style="max-width: 100px;">Req. Equivalencia</td>
                                      <td class="float-end">[[ invoiceDataJSON.total_sales_eqtax ]] €</td>
                                  </tr>
                                  {% endif %}
                                  <tr>
                                    <td style="max-width: 100px;">IRPF</td>
                                    <td :class="['float-end', getPosNegClass(invoiceDataJSON.total_sales_irpf), ]">
                                      [[ invoiceDataJSON.total_sales_irpf ]] €
                                    </td>
                                </tr>
                                  <tr>
                                      <td class="fw-bold text-black">{% trans "Total" %}</td>
                                      {% if is_self_employed and seller.eqtax == True %}
                                      <td class="float-end fw-bold text-black">{% trans "Base + IVA + Req. Eq."%}</td>
                                      {% else %}
                                      <td class="float-end fw-bold text-black">{% trans "Base + IVA" %}</td>
                                      {% endif %}
                                  </tr>
                              </table>
                          </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div class="col-md-12 col-lg-6 col-xl-4">
                <div class="card rounded-3 shadow-none border">
                  <div class="card-body">
                    <h6 class="mt-0">{% trans "Salidas" %}</h6>
                    <span class="my-2 h3 fw-bold">[[ invoiceDataJSON.total_expenses ]] €</span>
                    <i class="fas fa-level-up-alt float-end f-30 text-danger"></i>
                  </div>
                  <div class="card-body pt-0">
                    <div class="accordion-item border-0">
                      <h2 class="accordion-header" id="heading2">
                        <button
                          class="accordion-button p-0 collapsed fw-bold" type="button"
                          data-bs-toggle="collapse" data-bs-target="#collapse1"
                          aria-expanded="false" aria-controls="collapse1"
                        >{% trans "Ver detalles" %}</button>
                      </h2>
                      <div id="collapse1" class="accordion-collapse collapse" aria-labelledby="headingOne" data-bs-parent="#accordionExample" style="">
                          <div id="tableColapse2" class="pt-2">
                              <table class="table-striped" style="width: 100%;">
                                  <tr>
                                      <td style="max-width: 100px;">{% trans "Base" %}</td>
                                      <td :class="['float-end', ]">
                                        [[ invoiceDataJSON.total_expenses_amount ]] €
                                      </td>
                                  </tr>
                                  <tr>
                                      <td style="max-width: 100px;">IVA</td>
                                      <td :class="['float-end', ]">
                                        [[ invoiceDataJSON.total_expenses_vat ]] €
                                      </td>
                                  </tr>
                                  {% if is_self_employed and seller.eqtax == True %}
                                  <tr>
                                      <td style="max-width: 100px;">{% trans "Req. Equivalencia" %}</td>
                                      <td :class="['float-end',]">
                                        [[ invoiceDataJSON.total_expenses_eqtax ]] €
                                      </td>
                                  </tr>
                                  {% endif %}
                                  {% if is_self_employed and seller.is_direct_estimation  %}
                                  <tr>
                                      <td style="max-width: 100px;">
                                        {% trans "Gastos dif. just." %} &nbsp;
                                        <i
                                          role="button" class="fas fa-info-circle text-muted"
                                          data-bs-toggle="tooltip" data-bs-placement="top"
                                          data-bs-original-title="{% trans 'Gastos dificilmente justificables (5% beneficios)' %}"></i>
                                      </td>
                                      <td class="float-end">[[ invoiceDataJSON.total_sevent_porcent ]] €</td>
                                  </tr>
                                  {% endif %}
                                  <tr>
                                    <td style="max-width: 100px;">IRPF</td>
                                    <td :class="['float-end', getPosNegClass(invoiceDataJSON.total_expenses_irpf), ]">
                                      [[ invoiceDataJSON.total_expenses_irpf ]] €
                                    </td>
                                  </tr>
                                  <tr>
                                      <td class="fw-bold text-black">Total</td>
                                      {% if is_self_employed and seller.eqtax == True %}
                                        {% if seller.is_direct_estimation %}
                                          <td class="float-end fw-bold text-black">{% trans "Base + IVA + Req. Eq. + Gastos dif.just." %}</td>
                                        {% else  %}
                                          <td class="float-end fw-bold text-black">{% trans "Base + IVA + Req. Eq." %}</td>
                                        {% endif %}
                                      {% elif is_self_employed and seller.is_direct_estimation  %}
                                        <td class="float-end fw-bold text-black">{% trans "Base + IVA + Gastos dif.just." %}</td>
                                      {% else %}
                                        <td class="float-end fw-bold text-black">{% trans "Base + IVA" %}</td>
                                      {% endif %}
                                  </tr>
                              </table>
                          </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div class="col-md-12 col-lg-6 col-xl-4">
                <div class="card rounded-3 shadow-none border">
                  <div class="card-body">
                    <h6 class="mt-0">{% trans "Salidas" %}</h6>
                    <span class="my-2 h3 fw-bold">[[ invoiceDataJSON.total_expenses ]] €</span>
                    <i class="fas fa-level-up-alt float-end f-30 text-danger"></i>
                  </div>
                  <div class="card-body pt-0">
                    <div class="accordion-item border-0">
                      <h2 class="accordion-header" id="heading2">
                        <button
                          class="accordion-button p-0 collapsed fw-bold" type="button"
                          data-bs-toggle="collapse" data-bs-target="#collapse1"
                          aria-expanded="false" aria-controls="collapse1"
                        >{% trans "Ver detalles" %}</button>
                      </h2>
                      <div id="collapse1" class="accordion-collapse collapse" aria-labelledby="headingOne" data-bs-parent="#accordionExample" style="">
                          <div id="tableColapse2" class="pt-2">
                              <table class="table-striped" style="width: 100%;">
                                  <tr>
                                      <td style="max-width: 100px;">{% trans "Base" %}</td>
                                      <td :class="['float-end', ]">
                                        [[ invoiceDataJSON.total_expenses_amount ]] €
                                      </td>
                                  </tr>
                                  <tr>
                                      <td style="max-width: 100px;">{% trans "IVA" %}</td>
                                      <td :class="['float-end', ]">
                                        [[ invoiceDataJSON.total_expenses_vat ]] €
                                      </td>
                                  </tr>
                                  {% if is_self_employed and seller.eqtax == True %}
                                  <tr>
                                      <td style="max-width: 100px;">{% trans "Req. Equivalencia" %}</td>
                                      <td :class="['float-end',]">
                                        [[ invoiceDataJSON.total_expenses_eqtax ]] €
                                      </td>
                                  </tr>
                                  {% endif %}
                                  {% if is_self_employed and seller.is_direct_estimation  %}
                                  <tr>
                                      <td style="max-width: 100px;">
                                        {% trans "Gastos dif. just." %} &nbsp;
                                        <i
                                          role="button" class="fas fa-info-circle text-muted"
                                          data-bs-toggle="tooltip" data-bs-placement="top"
                                          data-bs-original-title="{% trans 'Gastos dificilmente justificables (5% beneficios)' %}"></i>
                                      </td>
                                      <td class="float-end">[[ invoiceDataJSON.total_sevent_porcent ]] €</td>
                                  </tr>
                                  {% endif %}
                                  <tr>
                                    <td style="max-width: 100px;">{% trans "IRPF" %}</td>
                                    <td :class="['float-end', getPosNegClass(invoiceDataJSON.total_expenses_irpf), ]">
                                      [[ invoiceDataJSON.total_expenses_irpf ]] €
                                    </td>
                                  </tr>
                                  <tr>
                                      <td class="fw-bold text-black">{% trans "Total" %}</td>
                                      {% if is_self_employed and seller.eqtax == True %}
                                        {% if seller.is_direct_estimation %}
                                          <td class="float-end fw-bold text-black">{% trans "Base + IVA + Req. Eq. + Gastos dif.just." %}</td>
                                        {% else  %}
                                          <td class="float-end fw-bold text-black">{% trans "Base + IVA + Req. Eq." %}</td>
                                        {% endif %}
                                      {% elif is_self_employed and seller.is_direct_estimation  %}
                                        <td class="float-end fw-bold text-black">{% trans "Base + IVA + Gastos dif.just." %}</td>
                                      {% else %}
                                        <td class="float-end fw-bold text-black">{% trans "Base + IVA" %}</td>
                                      {% endif %}
                                  </tr>
                              </table>
                          </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div class="col-md-12 col-lg-6 col-xl-4">
                <div class="card rounded-3 shadow-none border">
                  <div class="card-body">
                    <h6 class="mt-0">{% trans "Resultado" %}</h6>
                    <span :class="['my-2', 'h3', 'fw-bold', getPosNegClass(invoiceDataJSON.total_result), ]">
                      [[ invoiceDataJSON.total_result ]] €
                    </span>
                    <i class="fas fa-hand-holding-usd float-end f-30 text-primary"></i>
                  </div>
                  <div class="card-body pt-0">
                    <div class="accordion-item border-0">
                      <h2 class="accordion-header" id="heading3">
                        <button
                          class="accordion-button p-0 collapsed fw-bold" type="button"
                          data-bs-toggle="collapse" data-bs-target="#collapse1"
                          aria-expanded="false" aria-controls="collapse1"
                        >{% trans "Ver detalles" %}</button>
                      </h2>
                      <div id="collapse1" class="accordion-collapse collapse" aria-labelledby="headingOne" data-bs-parent="#accordionExample" style="">
                          <div id="tableColapse3" class="pt-2">
                              <table class="table-striped" style="width: 100%;">
                                  <tr>
                                      <td style="max-width: 100px;">{% trans "Base" %}</td>
                                      <td :class="['float-end', getPosNegClass(invoiceDataJSON.total_profit_amount), ]">
                                        [[ invoiceDataJSON.total_profit_amount ]] €
                                      </td>
                                  </tr>
                                  <tr>
                                      <td style="max-width: 100px;">{% trans "IVA" %}</td>
                                      <td :class="['float-end', getPosNegClass(invoiceDataJSON.total_profit_vat), ]">
                                        [[ invoiceDataJSON.total_profit_vat ]] €
                                      </td>
                                  </tr>
                                  {% if is_self_employed and seller.eqtax == True %}
                                  <tr>
                                      <td style="max-width: 100px;">{% trans "Req. Equivalencia" %}</td>
                                      <td :class="['float-end', getPosNegClass(invoiceDataJSON.total_profit_eqtax), ]">
                                        [[ invoiceDataJSON.total_profit_eqtax ]] €
                                      </td>
                                  </tr>
                                  {% endif %}
                                  <tr>
                                    <td style="max-width: 100px;">{% trans "IRPF" %}</td>
                                    <td :class="['float-end', getPosNegClass(invoiceDataJSON.total_profit_irpf), ]">
                                      [[ invoiceDataJSON.total_profit_irpf ]] €
                                    </td>
                                  </tr>
                                  <tr>
                                    <td class="fw-bold text-black">{% trans "Total" %}</td>
                                    {% if is_self_employed and seller.eqtax == True %}
                                    <td class="float-end fw-bold text-black">{% trans "Base + IVA + Req. Eq." %}</td>
                                    {% else %}
                                    <td class="float-end fw-bold text-black">{% trans "Base + IVA" %}</td>
                                    {% endif %}
                                  </tr>
                              </table>
                          </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

            </div>
          </div>
        {% endif %}
        <!-- Totals | END -->

        <div class="col-xl-12 col-sm-12">
          <div class="d-flex align-items-center gap-2 justify-content-end">
            <div class="col">
              <div class="input-group">
                <input class="form-control" type="search" id="search" name="search" placeholder="{% trans 'Buscar...' %}" maxlength="50" pattern="[a-zA-Z0-9@._\-+]+"/>
                <div class="input-group-append">
                  <button type="button" class="btn btn-outline-secondary" onclick="filter()" style="height: 100%;" id="searchBtn" disabled>
                    <i class="fa fa-search"></i>
                  </button>
                </div>
              </div>
            </div>

            <div class="list-action-btn-block">
              <button
                id="dropdownButton" class="btn btn-dark"
                type="button" title="{% trans 'Filtros' %}"
                >
                <i class="mdi mdi-filter-outline fa-xl me-0"></i>
                <span class="badge top-right-badge rounded-pill bg-danger d-none" id="id-filter-notification">2</span>
              </button>
              <div id="dropdownFiltersForm" class="dropdown-form shadow p-3">
                <form id="filtersFormID">
                  <div class="row mb-2">
                    <!-- status -->
                    <div class="col-12 mb-2">
                      <label for="multiple-status" class="mb-1">{% trans "Estado" %}</label>
                      <multi-status separator=", " value="" id="multiple-status">
                        <ul slot="check-values">
                          {% for status in invoice_statuses %}
                          <li
                            class="cursor-default"
                            id="multi-value"
                            value="{{ status.pk }}"
                            multi-title="{{ status.translated_description }}"
                          >{{ status.translated_description }}</li>
                          {% endfor %}
                        </ul>
                      </multi-status>
                    </div>
                    <!-- actividad económica -->
                    {% if economic_activity|length > 0 %}
                    <div class="col-12 mb-3">
                    {% else %}
                    <div class="col-12 mb-3" style="display:none;">
                    {% endif %}
                      <label for="multiple-economicactivities" class="mb-1">{% trans "Actividad Económica" %}</label>
                      <multi-economicactivity separator=", " value="" id="multiple-economicactivities">
                        <ul slot="check-values">
                          {% for iae in economic_activity %}
                          <li
                            class="cursor-default"
                            id="multi-value"
                            value="{{ iae.code }}"
                            multi-title="{{ iae.translated_description }} "
                          >{{ iae.translated_description }}</li>
                          {% endfor %}
                        </ul>
                      </multi-economicactivity>
                    </div>
                    <!-- años -->
                    <div class="col-6 mb-3">
                      <label for="year" class="mb-1">{% trans "Año" %}</label>
                      <select class="form-control form-select" name="year" id="year" onchange="onChangeYear();">
                        <option value="">{% trans "Todos los años" %}</option>
                        {% for year in years_with_invoices %}
                          <option value="{{ year }}">{{ year }}</option>
                        {% endfor %}
                      </select>
                    </div>
                    <!-- meses -->
                    <div class="col-6 mb-3">
                      <label for="multiple-month" class="mb-1">{% trans "Meses" %}</label>
                      <multi-month separator=", " value="" id="multiple-month">
                        <ul slot="check-values">
                          <li class="cursor-default" id="multi-value" value="1" multi-title="{% trans 'Enero' %}">{% trans "Enero" %}</li>
                          <li class="cursor-default" id="multi-value" value="2" multi-title="{% trans 'Febrero' %}">{% trans "Febrero" %}</li>
                          <li class="cursor-default" id="multi-value" value="3" multi-title="{% trans 'Marzo' %}">{% trans "Marzo" %}</li>
                          <li class="cursor-default" id="multi-value" value="4" multi-title="{% trans 'Abril' %}">{% trans "Abril" %}</li>
                          <li class="cursor-default" id="multi-value" value="5" multi-title="{% trans 'Mayo' %}">{% trans "Mayo" %}</li>
                          <li class="cursor-default" id="multi-value" value="6" multi-title="{% trans 'Junio' %}">{% trans "Junio" %}</li>
                          <li class="cursor-default" id="multi-value" value="7" multi-title="{% trans 'Julio' %}">{% trans "Julio" %}</li>
                          <li class="cursor-default" id="multi-value" value="8" multi-title="{% trans 'Agosto' %}">{% trans "Agosto" %}</li>
                          <li class="cursor-default" id="multi-value" value="9" multi-title="{% trans 'Septiembre' %}">{% trans "Septiembre" %}</li>
                          <li class="cursor-default" id="multi-value" value="10" multi-title="{% trans 'Octubre' %}">{% trans "Octubre" %}</li>
                          <li class="cursor-default" id="multi-value" value="11" multi-title="{% trans 'Noviembre' %}">{% trans "Noviembre" %}</li>
                          <li class="cursor-default" id="multi-value" value="12" multi-title="{% trans 'Diciembre' %}">{% trans "Diciembre" %}</li>
                        </ul>
                      </multi-month>
                    </div>
                    <!-- tax country -->
                    <div class="col-6 mb-3">
                      <label for="multiple-taxcountries" class="mb-1">{% trans "País de impuestos" %}</label>
                      <multi-taxcountry separator=", " value="" id="multiple-taxcountries">
                        <ul slot="check-values">
                          {% for country in invoices_tax_country %}
                            <li
                              class="cursor-default"
                              id="multi-value"
                              value="{{ country.pk }}"
                              multi-title="{{ country.translated_description }} ( {{ country.pk }} )"
                            >{{ country.translated_description }} ( {{ country.pk }} )</li>
                          {% endfor %}
                        </ul>
                      </multi-taxcountry>
                    </div>
                    <!-- ivas -->
                    <div class="col-6 mb-3">
                      <label for="multiple-vatrates" class="mb-1">{% trans "IVA's" %}</label>
                      <multi-vatrates separator=", " value="" id="multiple-vatrates" disabled>
                        <ul slot="check-values">
                        </ul>
                      </multi-vatrates>
                    </div>
                    <!-- tipos de facturas -->
                    <div class="col-6 mb-2">
                      <label for="multiple-invoicetype" class="mb-1">{% trans "Tipo de factura" %}</label>
                      <multi-invoicetype separator=", " value="" id="multiple-invoicetype">
                        <ul slot="check-values">
                          {% for invoice in invoice_type %}
                          <li
                              class="cursor-default"
                              id="multi-value"
                              value="{{ invoice.code }}"
                              multi-title="{{ invoice.translated_description }}"
                          >{{ invoice.translated_description }}</li>
                          {% endfor %}
                        </ul>
                      </multi-invoicetype>
                    </div>
                    <!-- tipos de transacciones -->
                    <div class="col-6 mb-3">
                      <label for="multiple-transactions" class="mb-1" id="multiple-transactions-label">{% trans "Tipo de transacción" %}</label>
                      <multi-transaction separator=", " value="" id="multiple-transactions">
                        <ul slot="check-values">
                          {% for transaction in transaction_types %}
                            <li
                              class="cursor-default" id="multi-value" value="{{ transaction.code }}"
                              multi-title="{{ transaction.description }}">
                              {{ transaction.description }}
                            </li>
                          {% endfor %}
                        </ul>
                      </multi-transaction>
                    </div>
                  </div>
                  <hr>
                  <div class="row">
                    <div class="col-12 text-end">
                      <button type="button" class="btn btn-light" onclick="resetFilters()">
                        {% trans "Limpiar" %}
                      </button>
                      <button type="button" id="applyFiltersButton" class="btn btn-dark" onclick="applyFilters()">
                        {% trans "Aplicar" %}
                      </button>
                    </div>
                  </div>
                </form>
              </div>
            </div>
            {% if category.pk != "sales" and category.pk != "expenses" %}
            <!-- details -->
            <div class="list-action-btn-block">
              <button
                id="toogleDetailButton" class="btn btn-dark" type="button"
                data-bs-toggle="collapse" data-bs-target="#collapse1"
                aria-expanded="false" aria-controls="collapse1">
                <i class="mdi mdi-eye fa-xl me-0"></i>
                <!-- {% trans "Detalles" %} -->
              </button>
            </div>
            {% endif %}

            <!-- download -->
            <div class="list-action-btn-block">
              <button
                class="btn btn-dark dropdown-toggle"
                type="button"
                data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="true">
                <i class="mdi mdi-download fa-xl me-0"></i>
                <!-- {% trans "Descargar" %} -->
              </button>
              <div
                class="dropdown-menu"
                style="position: absolute; inset: 0px auto auto 0px; margin: 0px; transform: translate(0px, 45px);"
                data-popper-placement="bottom-start">
                <a class="dropdown-item" role="button" onclick="downloadInvoices()">{% trans "Descargar facturas" %}
                  <!-- <a class="dropdown-item" data-bs-toggle="tooltip" data-bs-placement="top" title="{% trans 'Solo se descargarán las facturas que estén cargadas en la aplicación' %}" role="button" onclick="downloadInvoices()" >{% trans "Descargar facturas" %} -->
                  <span class="badge" style="background-color: #6c757d;">.zip</span>
                </a>
                <a class="dropdown-item" role="button" onclick="generateCSV()">{% trans "Exportar excel" %}
                  <span class="badge float-end bg-success">.xlsx</span>
                </a>
              </div>
            </div>

            <!-- pages -->
            <div class="list-action-btn-block">
              <select class="form-control form-select" name="show" id="show" onchange="filter()">
                <option value="50" default>50</option>
                <option value="100">100</option>
                <option value="200">200</option>
              </select>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- Bloque de Filtros | END -->

        <!-- Tabla Listado Facturas | START -->
        <div class="col-12">
          <div class="">
            <table
              id="invoices-table" class="table table-striped table-hover nowrap border"
              style="width:100%; overflow:hidden;">
              <thead class="table-head">
              <tr>
                <th>ID</th>
                <th>{% trans "Estado" %}</th>
                <th>{% trans "Número" %}</th>
                <th>{% trans "Documento" %}</th>
                <th>{% trans "Cliente" %}</th>
                <th>{% trans "Proveedor" %}</th>
                <th>{% trans "Cliente/Proveedor" %}</th>
                <th>{% trans "Fecha Contabilizacion" %}</th>
                <th>{% trans "Fecha Factura" %}</th>
                <th>{% trans "Fecha Expedición" %}</th>
                <th>{% trans "Fecha" %}</th>
                <th>{% trans "País IVA" %}</th>
                <th>{% trans "Categoría" %}</th>
                {% comment %} <th>{% trans "Tipo" %}</th> {% endcomment %}
                <th>{% trans "Tipo de Transacción" %}</th>
                <th>{% trans "IRPF (€)" %}</th>
                <th style="width:5%;">{% trans "Re.EQ (€)" %}</th>
                <th style="width:5%;">{% trans "IVA (€)" %}</th>
                <th style="width:5%;">{% trans "Base (€)" %}</th>
                <th style="width:5%;">{% trans "Total (€)" %}</th>
                <th>{% trans "Generada/Subida" %}</th>
                <th>{% trans "PDF Amazon" %}</th>
                <th>{% trans "Puede Borrar" %}</th>
                <th>{% trans "Acciones" %}</th>
              </tr>
              </thead>
              <tbody>
              </tbody>
            </table>
          </div>
        </div>
        <!-- Tabla Listado Facturas | END -->

      </div>
    </div>
  </div>

  <!-- modal for showing loading -->
  <div class="modal fade" id="loadingModal" tabindex="-1" aria-labelledby="successModalLabel" aria-hidden="true">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header justify-content-center">
          <h4 class="mt-2">{% trans "Descargando" %}</h4>
        </div>
        <div class="modal-body">
          <div id="spinner-animation" class="d-flex justify-content-center align-items-center text-center mb-3 d-none">
            <div class="spinner-grow text-success animation-delay-1 " role="status">
              <span class="sr-only">{% trans "Loading..." %}</span>
            </div>
            <div class="spinner-grow text-success animation-delay-2" role="status">
              <span class="sr-only">{% trans "Loading..." %}</span>
            </div>
            <div class="spinner-grow text-success animation-delay-3" role="status">
              <span class="sr-only">{% trans "Loading..." %}</span>
            </div>
          </div>
          <div id="folder-animation" class="folder-animation-wrapper mb-3">
            <div class="file-animation file-logo">
              <div class="page page1">
                <p class="mt-1" style="font-size: 12px;">{% trans "Factura 1" %}</p>
              </div>
              <div class="page page2">
                <p class="mt-1" style="font-size: 12px;">{% trans "Factura 2" %}</p>
              </div>
              <div class="page page3">
                <p class="mt-1" style="font-size: 12px;">{% trans "Factura 3" %}</p>
              </div>
            </div>
          </div>
          <div class="d-flex-column justify-content-center align-items-center text-center">
            <h4 id="textDownloadingAnimation" class="mb-2">{% trans "Preparando archivos..." %}</h4>
            <p style="font-size: 16px;">{% trans "Por favor, no cierres ni recargues la página" %}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- modal for showing loading -->

{% endblock content %}
{% block javascripts %}
  <!-- JQUERY DATA TABLE  -->
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery-3.6.0.min-v3.6.0.js"></script>
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery.dataTables.min-v1.10.25.js"></script>
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/dataTables/dataTables.select.min-v1.6.2.js"></script>
  <script src="{% static 'assets/js/plugins/sweetalert2.all.min.js' %}"></script>
  <script src="{% static 'assets/js/loading.js' %}"></script>
  <!-- VUE3 JS -->
  <script src="{% static 'assets/js/plugins/vue/3.2.6/vue.global.prod.js' %}"></script>
  <!-- start DEBUG -->
  <script type="text/javascript">
    const debug = {{ debug|yesno:"true,false" }};
    // Función para debug (imprime en consola solo si debug está habilitado)
    function debugLog(...args) {
      if (debug) {
        console.log(...args);
      }
    }

    debugLog(gettext('Debug mode is enabled'));
  </script>
  <!-- end DEBUG -->
  <script type="text/javascript">
    let table = null;
    let retryAttempts = 0;
    let timer;
    let filtersDictCount = {
      'year': false,
      'month': false,
      'multiple-taxcountries': false,
      'multiple-vatrates': false,
      'multiple-departurecountries': false,
      'multiple-arrivalcountries': false,
      'invoice_type': false,
      'multiple-transactions': false,
      'multiple-status': false,
      'multiple-economicactivities': false,
      'multiple-invoicetype': false,
      'multiple-month': false,
    }

    const Toast = Swal.mixin({
      toast: true,
      position: 'top-end',
      showConfirmButton: false,
      timer: 3000,
      timerProgressBar: true,
      didOpen: (toast) => {
        toast.addEventListener('mouseenter', Swal.stopTimer)
        toast.addEventListener('mouseleave', Swal.resumeTimer)
      }
    });

    // Definir estados y traducciones directamente desde Django
    const STATUS_TRANSLATIONS = {
      'Pendiente': '{% trans "Pendiente" %}',
      'Revision Pendiente': '{% trans "Revision Pendiente" %}',
      'Revisada': '{% trans "Revisada" %}',
      'Descartada': '{% trans "Descartada" %}'
    };

    const TRANSACTION_TYPES = {
      'Local Sale': '{% trans "Local Sale" %}',
      'Local Refund': '{% trans "Local Refund" %}',
      'Local Expense': '{% trans "Local Expense" %}',
      'Local Credit': '{% trans "Local Credit" %}',
      'Import DUA': '{% trans "Import DUA" %}',
      // ... añadir otros tipos de transacciones según sea necesario
    };

    const MESSAGES = {
      'no_invoices': '{% trans "No se han encontrado facturas." %}',
      'pagination_info': '{% trans "_START_ a _END_ de un total de _TOTAL_" %}',
      'search': '{% trans "Buscar:" %}',
      'no_results': '{% trans "No hay resultados que coincidan con su búsqueda." %}',
      'first': '{% trans "Primero" %}',
      'last': '{% trans "Último" %}',
      'previous': '{% trans "Anterior" %}',
      'next': '{% trans "Siguiente" %}'
    };

    const multipleStatus = document.getElementById('multiple-status');
    const multipleEconomicActivities = document.getElementById('multiple-economicactivities');
    const multipleMonth = document.getElementById('multiple-month');
    const multipleInvoiceType = document.getElementById('multiple-invoicetype');
    const multipleTaxCountries = document.getElementById('multiple-taxcountries');
    const multipleVatRates = document.getElementById("multiple-vatrates");
    const multipleDepartureCountries = document.getElementById('multiple-departurecountries');
    const multipleArrivalCountries = document.getElementById('multiple-arrivalcountries');
    const multiCheckbox = document.getElementById('multiple-transactions');
    const filterForm = document.getElementById('filtersFormID');

    const search_invoice = document.getElementById("search");
    const allowedCharsRegex = /^[a-zA-Z0-9@._\-+]*$/;

    const searchBtb = document.getElementById("searchBtn");

    search_invoice.addEventListener("keydown", (e) => {
      debugLog(gettext('resultado de la busqueda: '), e);
      if (e.key === "Enter") {
        clearTimeout(timer);
        filter();
      }
    });

    search_invoice.addEventListener("input", () => {
      if (search_invoice.value == '') {
        searchBtb.disabled = true;
        filter();
      }
      else {
        searchBtb.disabled = false;
      }
      if (!allowedCharsRegex.test(search_invoice.value)) {
        search_invoice.value = search_invoice.value.replace(/[^a-zA-Z0-9@._\-+]/g, '');
      }
    });

    const ajaxData = (d) => {
      debugLog('ajaxData | d: ', d);

      const getDataValue = (elementId) => {
        const el = document.getElementById(elementId);
        return el ? el.dataset.value : null;
      };

      const getValue = (elementId) => {
        const el = document.getElementById(elementId);
        return el ? el.value : null;
      };

      {% comment %}
      let economic_activity = null;
      if (document.getElementById("economic_activity")) {
        economic_activity = document.getElementById("economic_activity").value;
      }
      let status = document.getElementById("status").value;
      let year = document.getElementById("year").value;
      let month = document.getElementById("month").value;
      let country = document.getElementById("multiple-taxcountries").dataset.value;
      let multiple_transactions = document.getElementById("multiple-transactions").dataset.value;
      {% endcomment %}

      let multiple_status = getDataValue("multiple-status");
      let multiple_economic_activities = getDataValue("multiple-economicactivities");
      let year = getValue("year");
      let multiple_month = getDataValue("multiple-month");
      let country = getDataValue("multiple-taxcountries");
      let multiple_invoicetype = getDataValue("multiple-invoicetype");
      let multiple_transactions = getDataValue("multiple-transactions");
      let multiple_vat_rates = getDataValue("multiple-vatrates");

      let search = search_invoice.value;
      let show = getValue("show");
      let fc_transfer = false;
      let category = "";
      let tParams = "";

      {% if category and category.pk %}
        category = "{{category.pk}}";
      {% endif %}

      {% if transfer and transfer == True %}
        fc_transfer = true;
      {% endif %}

      {% if category and category.pk %}
        category = "{{category.pk}}";
      {% endif %}

      {% if transfer and transfer == True %}
        fc_transfer = true;
      {% endif %}

      if (multiple_economic_activities) {
        debugLog(gettext('filterDT | multiple_economic_activities: '), multiple_economic_activities);
        const selectedEconomicActivityValues = multiple_economic_activities.split(', ');
        d.iae = JSON.stringify(selectedEconomicActivityValues);
        tParams += "&iae=" + JSON.stringify(selectedEconomicActivityValues);
      }
      if (multiple_status) {
        debugLog(gettext('filterDT | multiple_status: '), multiple_status);
        const selectedStatusValues = multiple_status.split(', ');
        d.status_id = JSON.stringify(selectedStatusValues);
        tParams += '&status_id=' + JSON.stringify(selectedStatusValues);
      }

      if (year) {
        debugLog(gettext('filterDT | year: '), year);
        d.expedition_date_year = year;
        tParams += "&year=" + year;
      }

      if (multiple_month) {
        debugLog(gettext('filterDT | multiple_month: '), multiple_month);
        const selectedMonthValues = multiple_month.split(', ');
        d.months = JSON.stringify(selectedMonthValues);
        tParams += "&months=" + JSON.stringify(selectedMonthValues);;
      }

      if (multiple_invoicetype) {
        debugLog(gettext('filterDT | multiple_invoicetype: '), multiple_invoicetype);
        const selectedInvoiceTypeValues = multiple_invoicetype.split(', ');
        d.invoice_type_id = JSON.stringify(selectedInvoiceTypeValues);
        tParams += '&invoice_type_id=' + JSON.stringify(selectedInvoiceTypeValues);
      }

      if (country) {
        debugLog(gettext('filterDT | country: '), country);
        country = JSON.stringify(country.split(", "));
        d.tax_country_id = country;
        tParams += "&country=" + country;
      }

      if (multiple_vat_rates) {
        debugLog(gettext('filterDT | multiple_vat_rates: '), multiple_vat_rates);
        multiple_vat_rates = JSON.stringify(multiple_vat_rates.split(", "));
        d.vat_rate_id = multiple_vat_rates;
        tParams += "&vat_rate=" + multiple_vat_rates;
      }

      if (multiple_transactions) {
        debugLog(gettext('filterDT | multiple_transactions: '), multiple_transactions);
        const selectedValues = multiple_transactions.split(', ');
        d.transaction_type_id = JSON.stringify(selectedValues);
        tParams += '&transaction=' + JSON.stringify(selectedValues);
      }

      if (search) {
        debugLog(gettext('filterDT | search: '), search);
        d.search = search.trim();
        tParams += "&search=" + search.trim();
      }

      if (category) {
        debugLog(gettext('filterDT | category: '), category);
        d.invoice_category_id = category;
      }

      if (fc_transfer) {
        debugLog(gettext('filterDT | fc_transfer: '), fc_transfer);
        d.fc_transfer = fc_transfer;
        tParams += "&transfer=" + fc_transfer;
      }

      if (d.order.length > 0) {
        orderby = [];
        for (const o of d.order) {
          name = d.columns[o.column].data;
          if (name == 'contact') {
            orderby.push({"dir": o.dir, "name": "customer"});
            orderby.push({"dir": o.dir, "name": "provider"});
          } else if (name == 'date') {
            orderby.push({"dir": o.dir, "name": "expedition_date"});
            orderby.push({"dir": o.dir, "name": "invoice_date"});
            orderby.push({"dir": o.dir, "name": "accounting_date"});
          } else if (name == 'status') {
            orderby.push({"dir": o.dir, "name": "status__order"});
          } else {
            orderby.push({"dir": o.dir, "name": name});
          }
          orderby.push({"dir": 'desc', "name": "pk"});
        }
        d.order = JSON.stringify(orderby);
      }

      if(d.start == 0){
        getTotals(tParams);
      }

      return d;
    }

    const renderNumber = (data, type = null, row = null) => {
      if (data == null || data == undefined || data == "" || data == 0) {
        data = 0.0;
      } else {
        data = parseInt(parseFloat(data) * 100) / 100;
      }
      return data;
    }

    const numRound = (number) => {
      let round_num = Number((Math.abs(number) * 100).toPrecision(15));
      return Math.round(round_num) / 100 * Math.sign(number);
    }

    const createDT = () => {
      const seller = dj.value.seller;
      const irpf_visibility = seller && seller.contracted_accounting == true ? true : false;
      const eqtax_visibility = seller && seller.contracted_accounting == true && seller.eqtax == true ? true : false;
      const invoiceStatuses = {{ json.invoice_statuses|safe }};
      const accountExpenses = {{ json.account_expenses|safe }};
      const invoiceType = {{ json.invoice_type|safe }};
      table = $('#invoices-table').DataTable({
        scrollX: true,
        scrollY: '80vh',
        scrollCollapse: true,
        fixedHeader: true,
        fixedColumns: {
          leftColumns: 1
        },
        "serverSide": true,
        "ajax": {
          "url": "./data",
          "data": function (d) {
            ajaxData(d);
          },
          "error": function(xhr, error, thrown) {
            if(xhr.status === 500 && retryAttempts < 3) {
                retryAttempts++;
                // Reload DT
                setTimeout(function() {
                  table.ajax.reload(null, false)
                }, 1000 ); // Retry after 1 seconds

            } else {
                const Toast = Swal.mixin({
                  toast: true,
                  position: 'top-end',
                  showConfirmButton: false,
                  timer: 10000,
                  timerProgressBar: true,
                  didOpen: (toast) => {
                    toast.addEventListener('mouseenter', Swal.stopTimer)
                    toast.addEventListener('mouseleave', Swal.resumeTimer)
                  }
                });
                Toast.fire({
                  icon: 'error',
                  title: '{% trans "Ha Ocurrido un error en la carga de datos, por favor recargue la página." %}'
                });
            }
          },
        },
        "language": {
          "url": "https://cdn.datatables.net/plug-ins/1.10.25/i18n/Spanish.json"
        },
        "searching": false,
        "lengthChange": false,
        "lengthMenu": [[50, 100, 200], [50, 100, 200]],
        "order": [[1, 'asc'], [0, 'desc'],],
        "columns": [
          {"data": "pk", "visible": false},
          {
            "data": "status",
            "orderable": true,
            "render": function (data, type, row) {
              let html = "";
                if (row.status) {
                    let bg = "";
                    const matchingStatus = invoiceStatuses.find(
                      status => status.fields.translated_description === row.status
                    );
                    if (matchingStatus) {
                        switch(matchingStatus.fields.status_code) {
                          case "pending":
                          case "revision-pending":
                            bg = "bg-warning";
                            break;
                          case "revised":
                            bg = "bg-success";
                            break;
                          case "discard":
                            bg = "bg-danger";
                            break;
                          default:
                            bg = "bg-secondary";
                        }
                        html = `<span class="rounded ${bg} text-white p-1">
                                <b> &nbsp; ${matchingStatus.fields.translated_description} &nbsp; </b>
                                </span>`;
                    } else {
                        bg = "bg-secondary";
                        html = `<span class="rounded ${bg} text-white p-1">&nbsp; ${row.status} &nbsp;</span>`;
                    }
                }
                return html;
            }
          },
          {
            "data": "reference", className: "column-width-limit", "render": function (data, type, row) {
              if (type === 'display' && data.length > 40) {
                return data.substr(0, 40) + '...';
              }
              return data;
            }
          },
          {
            "data": "name",
            "visible": true,
            "render": function (data, type, row) {
              if (type === 'display' && data.length > 36) {
                return data.substr(0, 36) + '...';
              }
              return data;
            }
          },
          {"data": "customer", "visible": false},
          {"data": "provider", "visible": false},
          {
            "data": "contact", className: "column-width-limit", "render": function (data, type, row) {
              let contact = "";
              if (row.status == "pending" || "revision-pending") {
                contact = "-";
              } else {
                contact = "";
                if (row.customer) {
                  contact = row.customer.substr(0, 36) + '...';
                } else if (row.provider) {
                  contact = row.provider.substr(0, 36) + '...';
                }
              }
              return contact;
            }
          },
          {"data": "accounting_date", "visible": false},
          {"data": "invoice_date", "visible": false},
          {"data": "expedition_date", "visible": false},
          {
            "data": "date", "type": "date", "render": function (data, type, row) {
              let date = "";
              if (row.status == "pending" || row.status == "revision-pending") {
                date = "-";
              } else {
                date = "";
                if (row.accounting_date) {
                  date = row.accounting_date;
                } else if (row.expedition_date) {
                  date = row.expedition_date;
                } else if (row.invoice_date) {
                  date = row.invoice_date;
                }
              if (date) {
                const [year, month, day] = date.split('-');
                date = day + '/' + MONTH_NAMES[parseInt(month) - 1] + '/' + year;
              }
              }
              return date;
            }
          },
          {
            "data": "tax_country_id", "render": function (data, type, row) {
              let country = "";
              if (row.status == "pending" || row.status == "revision-pending") {
                country = "-";
              } else {
                country = row.tax_country_id || "";
              }
              return country;
            }
          },
          {"data": "invoice_category", "visible": false},

          {% comment %} {"data": "invoice_type", "render": function (data, type, row) {
            let invoice_type="";
            if (row.status == "pending" || row.status == "revision-pending"){
              invoice_type="-";
            }
            else{
              invoice_type = "";
              if (row.invoice_type) {
                invoice_type = row.invoice_type;
              }
            }
            return invoice_type;
          }, {% endcomment %}
          {
            "data": "transaction_type", "visible": false, "render": function (data, type, row) {
              let transaction_type = "";
              if (row.status == "pending" || row.status == "revision-pending") {
                transaction_type = "-";
              } else {
                transaction_type = row.transaction_type || "";
              }
              return transaction_type;
            }
          },
          {
            "data": "total_irpf_concp", "visible": irpf_visibility, "render": function (data, type, row) {
              let total_irpf_concp = "";
              if (row.status == "pending" || row.status == "revision-pending") {
                total_irpf_concp = "-";
              } else {
                total_irpf_concp = row.total_irpf_concp || "";
              }
              return total_irpf_concp;
            }
          },
          {
            "data": "total_eqtax_concp", "visible": eqtax_visibility, "render": function (data, type, row) {
              let total_eqtax_concp = "";
              if (row.status == "pending" || row.status == "revision-pending") {
                total_eqtax_concp = "-";
              } else {
                total_eqtax_concp = row.total_eqtax_concp || "";
              }
              return total_eqtax_concp;
            }
          },
          {
            "data": "vat_euros", "render": function (data, type, row) {
              let vat_euros = "";
              if (row.status == "pending" || row.status == "revision-pending") {
                vat_euros = "-";
              } else {
                vat_euros = row.vat_euros || "";
              }
              return vat_euros;
            }
          },
          {
            "data": "amount_euros", "render": function (data, type, row) {
              let amount_euros = "";
              if (row.status == "pending" || row.status == "revision-pending" ||
                  row.transaction_type == "Import DUA") {
                amount_euros = "-";
              } else {
                amount_euros = row.amount_euros || "";
              }
              return amount_euros;
            }
          },
          {
            "data": "total_euros_concp", "render": function (data, type, row) {
              let total_euros_concp = "";
              if (row.status == "pending" || row.status == "revision-pending") {
                total_euros_concp = "-";
              } else {
                total_euros_concp = "";
                if (row.transaction_type == "Import DUA") {
                  total_euros_concp = row.vat_euros;
                } else if (row.total_euros_concp) {
                  total_euros_concp = row.total_euros_concp;
                }
              }
              return total_euros_concp;
            }
          },
          {"data": "is_generated", "visible": false},
          {"data": "pdf_amazon", "visible": false},
          {"data": "can_delete", "visible": false},
          {
            "data": "file", "orderable": false, "render": function (data, type, row) {
              let html = "";
              let button1 = "";
              let button2 = "";
              let button3 = "";
              let button4 = "";
              let button5 = "";

              if (row.pk) {
                button1 = `
                      <a
                      class="btn btn-success btn-icon tooltip-wrapper tooltip-button"
                      href="{% url 'app_sellers:summary' seller.shortname %}invoice/${row.pk}"
                      data-bs-toggle="tooltip"
                      data-bs-placement="top"
                      data-bs-original-title="{% trans 'Ver Factura' %}">
                        <i class="fas fa-eye"></i>
                      </a>
                    `;
              }

              if (row.file) {
                button2 = `
                    <a
                        class="btn btn-info btn-icon tooltip-wrapper tooltip-button"
                        href="/media/${row.file}"
                        target="_blank"
                        data-bs-toggle="tooltip"
                        data-bs-placement="top"
                        data-bs-original-title="{% trans 'Descargar Factura' %}"
                        download>
                      <i class="fas fa-download"></i>
                    </a>
                  `;
              } else if (row.is_generated == "True") {
                button2 = `
                    <a  href="{% url 'app_sellers:summary' seller.shortname %}invoice/${row.pk}/file/"
                        class="btn btn-info btn-icon tooltip-wrapper tooltip-button"
                        target="_blank"
                        data-bs-toggle="tooltip"
                        data-bs-placement="top"
                        data-bs-original-title="{% trans 'Descargar Factura' %}"
                        download >
                      <i class="fa-solid fa-download"></i>
                    </a>
                  `;
              }

              if (row.pdf_amazon) {
                button3 = `
                    <a
                      class="btn btn-icon btn-dark tooltip-wrapper tooltip-button"
                      href="${row.pdf_amazon}" target="_blank"
                      data-bs-toggle="tooltip"
                      data-bs-placement="top"
                      data-bs-original-title="{% trans 'Ver Factura en Amazon' %}">
                        <i class="fab fa-amazon"></i>
                    </a>
                  `;
              }
              if (row.pk) {
                if (row.status != "revised" || (row.status == 'revised' && row.is_generated == "True" && row.can_delete == "True")) {
                  button4 = `
                      <a class="btn btn-danger btn-icon"
                        data-bs-toggle="tooltip"
                        data-bs-placement="top"
                        data-bs-original-title="{% trans 'Eliminar Factura' %}"
                        href="{% url 'app_sellers:summary' seller.shortname %}invoice/${row.pk}/delete">
                        <i class="feather icon-trash-2"></i>
                      </a>
                    `;
                }
              }
              if (row.is_generated == "True" && row.is_rectifying != "True" && row.status == "Revisada") {
                button5 = `
                    <a  href="{% url 'app_invoices:seller_invoice_new' seller.shortname %}?rectInv=${row.reference}&customer=${row.customer_pk}"
                        class="btn btn-icon tooltip-wrapper tooltip-button"
                        style="background-color: #FE8330; color: white;"
                        data-bs-toggle="tooltip"
                        data-bs-placement="top"
                        data-bs-original-title="{% trans 'Emitir Factura Rectificativa' %}">
                      <i class="fa-solid fa-file-pen"></i>
                    </a>
                  `;
              }
              //------------------------------------------
              if (row.pk || row.file) {
                html = `
                    <div class=" text-center justify-content-center">
                        <button type="button" class="btn btn-secondary border  text-nowrap" data-bs-toggle ="dropdown" aria-expanded="false">
                          <i class="fa-solid fa-xl fa-ellipsis m-0"></i>
                        </button>

                        <ul class="dropdown-menu dropdown-menu-end">
                          <li class= mb-1>${button1}</li>
                          <li class= mb-1>${button2}</li>
                          <li class= mb-1>${button3}</li>
                          <li>${button4}</li>
                          <li>${button5}</li>
                        </ul>
                    </div>
                  `;
                htmlseparated = `
                  ${button1}
                  ${button2}
                  ${button3}
                  ${button4}
                  ${button5}
                  `;
              }
              return htmlseparated;

            }
          },
          {"data": "is_rectifying", "visible": false},
          {"data": "customer_pk", "visible": false }
        ],
        "language": {
          "lengthMenu": "{% trans '_MENU_' %}",
          "zeroRecords": "{% trans 'No se han encontrado facturas' %}",
          "info": "{% trans '_START_ a _END_ de un total de _TOTAL_' %}",
          "search": "{% trans 'Buscar' %}:",
          "infoEmpty": "{% trans 'No hay resultados que coincidan con su búsqueda' %}",
          "infoFiltered": "",
          "paginate": {
            "first": "{% trans 'Primero' %}",
            "last": "{% trans 'Último' %}",
            "previous": "{% trans 'Anterior' %}",
            "next": "{% trans 'Siguiente' %}"
          }
        },
        "drawCallback": function (settings) {
          $('[data-bs-toggle="tooltip"]').tooltip();
        },
      });
      filter();

      $('#page-length').change(function () {
        table.page.len($(this).val()).draw();
      });

      $(table.table().node()).on('xhr.dt', function (e, settings, json) {
        if (inputExportCSV.value == true) {
          exportTableToCSV();
        }
      });
    }

    $(document).ready(function () {
      const seller = dj.value.seller;
      const categoryTable = '{{ category }}' || 'All';
      const filterCookieName = `DataTable_Invoices_List_${categoryTable}_${seller.shortname}`;
      const invoicesURL = window.location.href;
      const currentYear = new Date().getFullYear();
      const yearInput = document.getElementById('year');

      const taxCountry = document.getElementById("multiple-taxcountries");

      if (taxCountry) {
        onTaxCountryChange();
        taxCountry.addEventListener('change', onTaxCountryChange);
      } else {
        console.warn(gettext('El elemento multiple-taxcountries no está disponible en el DOM.'));
      }
      onChangeYear()
      addFiltersBadge(yearInput);

      dropdownFilterFormHack();
      createDT();
      massiveOptionPerm();

      if (!(invoicesURL.includes("/invoices/sales")) && !(invoicesURL.includes("/invoices/expenses"))) {
        checkCollapse();
        $('#collapse1').on('hidden.bs.collapse', function () {
          const toogleDetailButton = document.getElementById("toogleDetailButton");
          toogleDetailButton.innerHTML = `<i class="mdi mdi-eye fa-xl me-0"></i>`;
        });
        $('#collapse1').on('shown.bs.collapse', function () {
          const toogleDetailButton = document.getElementById("toogleDetailButton");
          toogleDetailButton.innerHTML = `<i class="mdi mdi-eye-off fa-xl me-0"></i>`;
        });
      }

      const dropdownButton = document.getElementById('dropdownButton');
      const dropdownFiltersForm = document.getElementById('dropdownFiltersForm');

      dropdownButton.addEventListener('click', function (event) {
        dropdownFiltersForm.style.display = dropdownFiltersForm.style.display === 'block' ? 'none' : 'block';
        if (dropdownFiltersForm.style.display === 'block') {
          dropdownFiltersForm.scrollIntoView({behavior: "smooth", block: "center", inline: "center"});
        }
      });

      document.addEventListener('click', function (event) {
        if (!dropdownFiltersForm.contains(event.target) && !dropdownButton.contains(event.target)) {
          dropdownFiltersForm.style.display = 'none';
        }
      });

      dropdownFiltersForm.addEventListener('click', function (event) {
        hideMultiDropDown(event);
        event.stopPropagation();

      });

      function hideMultiDropDown(event) {
        const multiSelectorElements = document.querySelectorAll('[separator]');

        multiSelectorElements.forEach((element) => {
          if (!event.composedPath().includes(element) && typeof element.hideDropDown === 'function') {
            element.dropDownVisible = false;
            element.hideDropDown();
          }
        });
      }


      addListenerToMultiSelector("multiple-status");
      addListenerToMultiSelector("multiple-economicactivities");
      addListenerToMultiSelector("multiple-month");
      addListenerToMultiSelector("multiple-taxcountries", onTaxCountryChange);
      addListenerToMultiSelector("multiple-vatrates");
      addListenerToMultiSelector("multiple-transactions");
      addListenerToMultiSelector("multiple-invoicetype");

      handleMultiCheckboxChange(multipleStatus);
      handleMultiCheckboxChange(multipleEconomicActivities);
      handleMultiCheckboxChange(multipleMonth);
      handleMultiCheckboxChange(multipleTaxCountries, onTaxCountryChange);
      handleMultiCheckboxChange(multipleVatRates);
      handleMultiCheckboxChange(multiCheckbox);
      handleMultiCheckboxChange(multipleInvoiceType);

      const selectFilters = filterForm.querySelectorAll('select');
      selectFilters.forEach(select =>
        select.addEventListener('change', () => {
          addFiltersBadge(select);
          checkFilterState();
        })
      );

      resetCookie(filterCookieName);
      toggleApplyFiltersButton(false);

      // Manejar el chevron del selector de filas por página
      const showSelect = document.getElementById('show');
      if (showSelect) {
        let isDropdownOpen = false;

        // Detectar cuando se abre el dropdown
        showSelect.addEventListener('mousedown', function(e) {
          // Pequeño delay para permitir que el dropdown se abra
          setTimeout(() => {
            if (document.activeElement === showSelect) {
              isDropdownOpen = true;
              this.classList.add('dropdown-open');
            }
          }, 10);
        });

        // Detectar cuando se cierra el dropdown
        showSelect.addEventListener('blur', function() {
          isDropdownOpen = false;
          this.classList.remove('dropdown-open');
        });

        showSelect.addEventListener('change', function() {
          isDropdownOpen = false;
          this.classList.remove('dropdown-open');
        });

        // Manejar teclas (Enter, Escape, etc.)
        showSelect.addEventListener('keydown', function(e) {
          if (e.key === 'Escape' || e.key === 'Enter') {
            isDropdownOpen = false;
            this.classList.remove('dropdown-open');
          }
        });
      }

    });

    const filter = () => {
      let show = document.getElementById("show").value;
      if (show) {
        table.page.len(show);
      }
      table.draw();
    }

    //***** HANDLING FILTERS AND COOKIES ********//
    function filtersSelected() {
      const form = document.getElementById("dropdownFiltersForm");
      const multiSelectors = document.querySelectorAll('[separator]');
      const selectInputs = form.querySelectorAll('select');
      let hasValue = false;

      return Array.from(selectInputs).some(select => select.value) || Array.from(multiSelectors).some(multiSelector => multiSelector.dataset.value);
    }

    function toggleApplyFiltersButton(state) {
      const applyFiltersButton = document.getElementById("applyFiltersButton");
      applyFiltersButton.disabled = !state;
    }

    function applyFilters() {
      if (checkFilterState()) {
        filter();
      }
      setCookieFilterState();
      toggleApplyFiltersButton(false);

      // Cerrar el desplegable de filtros con un pequeño retraso
      const dropdownFiltersForm = document.getElementById('dropdownFiltersForm');
      setTimeout(() => {
        dropdownFiltersForm.style.display = 'none';
      }, 2000); // 2 segundos
    }

    // Restablecer todos los filtros
    function resetFilters() {
      const form = document.getElementById("dropdownFiltersForm");
      const multiSelectors = document.querySelectorAll('[separator]');
      const selectInputs = form.querySelectorAll('select');
      // Restablecer los valores de los selects
      selectInputs.forEach((select) => {
        select.value = '';
      });
      // Restablecer los valores de los multi-selectores
      multiSelectors.forEach((multiSelector) => {
        multiSelector.dataset.value = ''; // Resetear
        multiSelector.value = '';
        multiSelector.updateItems(); // Actualizar
      });
      // Llamada a las funciones para deshabilitar los selectores
      onTaxCountryChange();
      onChangeYear();
      // Filtrar los resultados y actualizar el estado del botón
      filter();
      clearFiltersBadge();
      setCookieFilterState();
      toggleApplyFiltersButton(false);
      // Cerrar el desplegable de filtros con un pequeño retraso
      const dropdownFiltersForm = document.getElementById('dropdownFiltersForm');
      setTimeout(() => {
        dropdownFiltersForm.style.display = 'none';
      }, 2000); // 2 segundos
    }

    // Añadir badge de filtros si se han aplicado
    function addFiltersBadge(element) {
      const elementID = element.id;
      filtersDictCount[elementID] = !!element.value || !!element.dataset.value;

      const badgeNumber = Object.values(filtersDictCount).filter(value => value === true).length;
      const badge = document.getElementById('id-filter-notification');

      if (badgeNumber > 0) {
        badge.classList.remove('d-none');
        badge.innerHTML = badgeNumber;
      } else {
        badge.classList.add('d-none');
        badge.innerHTML = '';
      }
    }

    const clearFiltersBadge = () => {
      Object.keys(filtersDictCount).forEach(key => filtersDictCount[key] = false);
      const badge = document.getElementById('id-filter-notification');
      badge.classList.add('d-none');
      badge.innerHTML = '';
    }

    function getFilterValues() {
      const form = document.getElementById("dropdownFiltersForm");
      const multiSelectors = document.querySelectorAll('[separator]');
      const selectInputs = form.querySelectorAll('select');
      let filterDict = {};

      selectInputs.forEach((select) => {
        filterDict[select.id] = select.value;
      });

      multiSelectors.forEach((multiSelector) => {
        if (multiSelector.dataset.value) {
          filterDict[multiSelector.id] = multiSelector.dataset.value.split(multiSelector.getAttribute('separator'));
        } else {
          filterDict[multiSelector.id] = [];
        }
      });

      return filterDict;
    }

    function checkFilterState(){
      const seller = dj.value.seller;
      const currentFilter = getFilterValues();
      const categoryTable = '{{ category }}' || 'All';
      const cookieName = `DataTable_Invoices_List_${categoryTable}_${seller.shortname}`;
      const cookieFilter = JSON.parse(getCookie(cookieName) || '{}');

      toggleApplyFiltersButton(JSON.stringify(currentFilter) !== JSON.stringify(cookieFilter));

      return JSON.stringify(currentFilter) !== JSON.stringify(cookieFilter);
    }

    function getCookie(name) {
      const cookie = document.cookie.split('; ').find(row => row.startsWith(name + '='));
      return cookie ? decodeURIComponent(cookie.split('=')[1]) : null;
    }

    function setCookieFilterState() {
      const seller = dj.value.seller;
      const filter = getFilterValues();
      const categoryTable = '{{ category }}' || 'All';
      const cookieName = `DataTable_Invoices_List_${categoryTable}_${seller.shortname}`;
      document.cookie = `${cookieName}=${JSON.stringify(filter)}; path=/`;
    }

    function resetCookie(name) {
      const defaultValues = getFilterValues();
      document.cookie = `${name}=${JSON.stringify(defaultValues)}; path=/`;
    }
    //***** HANDLING FILTERS AND COOKIES ********//

    const getTotals = (params) => {
      let p = params;

      if (!p || p == undefined || p == null || p == "") {
        p = "";
      } else if (p.charAt(0) == "&") {
        p[0] = "?";
      }

      invoiceDataJSON.value = {
        "status_array": [],
        "months_array": [],
        "country": [],
        "year": "all",
        "economic_activity": [],
        "regime": [],
        "transaction_type": [],
        "invoice_type": [],
        "invoices_count": 0,

        // Totales de ventas
        "total_sales_amount": 0,
        "total_sales_vat": 0,
        "total_sales_irpf": 0,
        "total_sales_eqtax": 0,
        "total_sales": 0,

        // Totales de gastos
        "total_expenses_amount": 0,
        "total_expenses_vat": 0,
        "total_expenses_irpf": 0,
        "total_expenses_eqtax": 0,
        "total_expenses": 0,

        // Porcentaje de gastos deducibles al 70%
        "total_sevent_porcent": 0,

        // Totales de beneficios
        "total_profit_amount": 0,
        "total_profit_vat": 0,
        "total_profit_irpf": 0,
        "total_profit_eqtax": 0,
        "total_result": 0
      };

      const url = "{% url 'app_invoices:seller_invoices_json' seller.shortname %}?" + p;
      $.ajax({
        url: url,
        type: "GET",
        dataType: "JSON",
        success: function (data) {

          // status
          let s = document.getElementById("multiple-status");
          s = s && s.dataset.value ? s.dataset.value.toString().toLowerCase().replaceAll(' ', '') : null;
          const ds = data.status_array ? data.status_array.join(',').toString().toLowerCase().trim().replaceAll(' ', '') : null;
          debugLog(`multiple-status es ds=: ${ds}`);

          // Economic activity
          let ea = document.getElementById("multiple-economicactivities");
          ea = ea && ea.dataset.value ? ea.dataset.value.toString().toLowerCase().replaceAll(' ', '') : null;
          const dea = data.economic_activity ? data.economic_activity.join(',').toString().toLowerCase().trim().replaceAll(' ', '') : null;
          debugLog(`multiple-status es ds=: ${ds}`);

          // year and month
          const y = document.getElementById("year").value.toString().toLowerCase().trim();
          let m = document.getElementById("multiple-month");
          m = m && m.dataset.value ? m.dataset.value.toString().toLowerCase().replaceAll(' ', '') : null;
          const dm = data.months_array ? data.months_array.join(',').toString().toLowerCase().trim().replaceAll(' ', '') : null;
          debugLog(`multiple-month es dm=: ${dm}`);

          // invoice types
          let it = document.getElementById("multiple-invoicetype");
          it = it && it.dataset.value ? it.dataset.value.toString().toLowerCase().replaceAll(' ', '') : null;
          const dit = data.invoice_type ? data.invoice_type.join(',').toString().toLowerCase().trim().replaceAll(' ', '') : null;
          debugLog(`multiple-transactions es dit=: ${dit}`);

          // Tax country
          let c = document.getElementById("multiple-taxcountries");
          c = c && c.dataset.value ? c.dataset.value.toString().toLowerCase().replaceAll(' ', '') : null;

          // Multiple transaction
          let mt = document.getElementById("multiple-transactions");
          mt = mt && mt.dataset.value ? mt.dataset.value.toString().toLowerCase().replaceAll(' ', '') : null;
          const dmt = data.transaction_type ? data.transaction_type.join(',').toString().toLowerCase().trim().replaceAll(' ', '') : null;
          debugLog(`multiple-transactions es dmt=: ${dmt}`);

          let sameIAE = false;
          let sameStatus = false;
          let sameMonth = false;
          let sameInvoiceType = false;
          let sameYear = false;
          let sameCountry = false;
          let sameMultipleTransactions = false;

          if (
            (s && ds && s === ds) || (ds === "" && !s)) {
            sameStatus = true;
            debugLog(`sameStatus es ea=: ${sameStatus}`);
          }

          if ((ea && dea && ea === dea) ||(dea === "" && !ea)) {
            sameIAE = true;
            debugLog(`sameIAE es ea=: ${sameIAE}`);
          }

          if ((m && dm && m === dm) ||(dm === "" && !m)) {
            sameMonth = true;
          }

          if ((mt && dmt && mt === dmt) || (dmt === "" && !mt)) {
            sameMultipleTransactions = true;
          }

          if ((it && dit && it === dit) || (dit === "" && !it)) {
            sameInvoiceType = true;
          }

          if (y == data.year || (data.year == "all" && !y)) {
            sameYear = true;
          }

          if (
            (c && data.country && c === data.country.join(',').toString().toLowerCase().trim()) ||
            (data.country.toString().toLowerCase().trim() == "" && !c)
          ) {
            sameCountry = true;
          }

          if (sameIAE && sameStatus && sameYear && sameMonth && sameCountry && sameInvoiceType && sameMultipleTransactions) {
            // Aquí se guarda la peticion
            invoiceDataJSON.value = data;
          }
        },
        error: function (jqXHR, textStatus, errorThrown) {
          // Aquí se ejecuta el código cuando hay un error en la petición
          console.error("Error en la peticion AJAX")
        }
      });
    }

    const onClickExportCSV = () => {
      inputExportCSV.value = true;
      document.getElementById("show").value = -1;
      table.page.len(-1);
      table.draw();
    }

    const exportTableToCSV = async () => {
      // Disable Export CSV
      inputExportCSV.value = false;

      // Obtenemos los datos de la tabla
      let data = table.ajax.json().data;

      // Creamos la variable CSV
      let csv = '\ufeff';

      // Creamos el encabezado de las columnas
      let header = [];

      // Creamos la lista de columnas a excluir
      const exclude = ["contact", "Cliente/Proveedor", "invoice_date", "Fecha Factura", "date", "Fecha", "file", "Acciones", "pdf_amazon", "PDF Amazon", "is_rectifying"];

      // Creamos el separador de columnas
      const separator = ";";

      // Obtenemos los encabezados de las columnas
      table.columns().every(function () {
        let temp_header = [];
        temp_header.push(this.header().innerText);
        for (item of temp_header) {
          if (!exclude.includes(item)) {
            header.push(item);
          }
        }
      });

      // Agregamos los encabezados al string CSV
      csv += header.join(separator) + '\n';

      // Agregamos los datos al string CSV
      data.forEach(function (row) {
        var rowData = [];
        // Recorremos cada celda de la fila
        for (var prop in row) {
          if (!exclude.includes(prop)) {
            if (!(prop.includes("€") || prop.includes("euros") || prop.includes("total"))) {
              rowData.push(row[prop]);
            } else {
              let data = row[prop];
              data = renderNumber(data);
              let dataEsp = data.toString().replace(',', '').replace('.', ',');
              rowData.push(dataEsp)
            }
          }
        }
        csv += rowData.join(separator) + '\n';
      });

      // Descargamos el archivo CSV
      let csvData = 'data:text/csv;charset=utf-8,' + encodeURIComponent(csv);
      csvData = csvData.replace("&amp;", "&");
      let link = document.createElement('a');
      link.href = csvData;
      {% if seller.shortname %}
        link.download = 'facturas_{{seller.shortname}}.csv';
      {% else %}
        link.download = 'facturas.csv';
      {% endif %}
      link.click();

      // Disable Export CSV
      inputExportCSV.value = false;
    }

    // Obtiene valores de filtros seleccionados
    function prepareFilterData() {
      // transactions
      let multiple_transactions = document.getElementById("multiple-transactions").dataset.value;
      let selectedTransactionValues = multiple_transactions ? multiple_transactions.split(', ') : [];
      // departurecountries
      let selectedDepartureCountryValues = [];
      // arrivalcountries
      let selectedArrivalCountryValues = [];
      // taxcountries
      let multiple_taxcountries = document.getElementById("multiple-taxcountries").dataset.value;
      let selectedTaxCountryValues = multiple_taxcountries ? multiple_taxcountries.split(', ') : [];
      // vatrates
      let multiple_vatrates = document.getElementById("multiple-vatrates").dataset.value;
      let selectedVatRateValues = multiple_vatrates ? multiple_vatrates.split(', ') : [];
      // status
      let multiple_status = document.getElementById("multiple-status").dataset.value;
      let selectedStatusValues = multiple_status ? multiple_status.split(', ') : [];
      // economic_activity
      let multiple_economic_activity = document.getElementById("multiple-economicactivities").dataset.value;
      let selectedeconomicactivityValues = multiple_economic_activity ? multiple_economic_activity.split(', ') : [];
      // month
      let multiple_month = document.getElementById("multiple-month").dataset.value;
      let selectedMonthValues = multiple_month ? multiple_month.split(', ') : [];
      // invoice_type
      let multiple_invoicetype = document.getElementById("multiple-invoicetype").dataset.value;
      let selectedInvoiceTypeValues = multiple_invoicetype ? multiple_invoicetype.split(', ') : [];

      let seachInput = document.getElementById("search").value;
      let currentURL = window.location.href;
      let invoice_type;

      if (currentURL.includes("/invoices/sales")) {
        invoice_type = "sales";
      } else if (currentURL.includes("/invoices/expenses")) {
        invoice_type = "expenses";
      } else if (currentURL.includes("/invoices/transfers")) {
        invoice_type = "transfers";
      } else {
        invoice_type = "all";
      }

      return {
        invoice_status: selectedStatusValues,
        iae: selectedeconomicactivityValues,
        year: document.getElementById("year").value,
        month: selectedMonthValues,
        tax_country_id: selectedTaxCountryValues,
        departure_country_id: selectedDepartureCountryValues,
        arrival_country_id: selectedArrivalCountryValues,
        vat_rate_id: selectedVatRateValues,
        transaction_type: selectedTransactionValues,
        invoice_type: invoice_type,
        search: seachInput,
        invoice_type_id: selectedInvoiceTypeValues,
      };

    }

    const generateCSV = async () => {
      const modalLoading = new bootstrap.Modal(document.getElementById('loadingModal'), {
        keyboard: false,
        backdrop: 'static'
      });
      modalLoading.show();
      await new Promise(r => setTimeout(r, 1000));

      let dataFiltered = prepareFilterData();
      let jsonData = JSON.stringify(dataFiltered);

      const generateCSVResponse = await fetch("{% url 'app_invoices:generate_csv' seller.shortname %}", {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRFToken': "{{ csrf_token }}"
        },
        body: jsonData
      });


      if (generateCSVResponse.status == 200) {
        const blob = await generateCSVResponse.blob();
        const blobUrl = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = blobUrl;
        link.setAttribute('download', `Listado_facturas_{{seller.shortname}}.xlsx`);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        Toast.fire({
          icon: 'success',
          title: generateCSVResponse.headers.get('X-Message')
        });

      } else if (generateCSVResponse.status == 204) {
        Toast.fire({
          icon: 'warning',
          title: generateCSVResponse.headers.get('X-Message')
        });
      } else if (generateCSVResponse.status == 500) {
        Toast.fire({
          icon: 'error',
          title: generateCSVResponse.headers.get('X-Message')
        });
      } else {
        Toast.fire({
          icon: 'error',
          title: 'Ha surgido un error al generar el archivo'
        });
      }
      modalLoading.hide();
    }

    const downloadInvoices = async () => {
      const shortname = "{{seller.shortname}}";
      const modalLoading = new bootstrap.Modal(document.getElementById('loadingModal'), {
        keyboard: false,
        backdrop: 'static'
      });
      modalLoading.show();
      await new Promise(r => setTimeout(r, 1000));

      let multiple_transactions = document.getElementById("multiple-transactions").dataset.value;
      let selectedTransactionValues = multiple_transactions ? multiple_transactions.split(', ') : [];
      let seachInput = document.getElementById("search").value;
      let currentURL = window.location.href;
      let invoice_type;

      if (currentURL.includes("/invoices/sales")) {
        invoice_type = "sales";
      } else if (currentURL.includes("/invoices/expenses")) {
        invoice_type = "expenses";
      } else if (currentURL.includes("/invoices/transfers")) {
        invoice_type = "transfers";
      } else {
        invoice_type = "all";
      }

      let dataFiltered = {
        invoice_status: document.getElementById("status").value,
        year: document.getElementById("year").value,
        month: document.getElementById("month").value,
        tax_country_id: document.getElementById("country").value,
        departure_country_id: "",
        transaction_type: selectedTransactionValues,
        invoice_type: invoice_type,
        search: seachInput
      };

      try {

        const invoicesResponse = await fetch("{% url 'app_invoices:seller_invoices_massive_download' seller.shortname %}", {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': "{{ csrf_token }}"
          },
          body: JSON.stringify(dataFiltered)
        });

        if (invoicesResponse.status == 200) {
          modalLoading.hide();
          const blob = await invoicesResponse.blob();
          const blobUrl = window.URL.createObjectURL(blob);
          const link = document.createElement('a');
          link.href = blobUrl;
          link.setAttribute('download', `Facturas_${shortname}.zip`);
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          Toast.fire({
            icon: 'success',
            title: '{% trans "Facturas descargadas correctamente"%}'
          });
        } else if (invoicesResponse.status == 204) {
          Toast.fire({
            icon: 'warning',
            title: '{% trans "No hay facturas para descargar"%}'
          });
        } else {
          Toast.fire({
            icon: 'error',
            title: '{% trans "Ha surgido un error al descargar las facturas"%}'
          });
        }
      } catch (error) {
        console.error("Error in downloadInvoices: ", error);
      }

      modalLoading._element.addEventListener('shown.bs.modal', function () {
        modalLoading.hide();
        $('#downloadModal').modal('hide');
      });
    }

    // DYNAMIC HANDLING FOF MULTICHECK FILTERS
    function handleMultiCheckboxChange(element, callback) {
      // Verificar si el elemento existe
      if (!element) {
        console.warn("Elemento no encontrado para manejar el cambio de checkbox múltiple.");
        return; // Salir de la función si el elemento no existe
      }

      // Si el elemento existe, aplicar el evento
      element.addEventListener('change', async function () {
        addFiltersBadge(element);
        checkFilterState();
        if (callback) await callback();
      });
    }

    function addListenerToMultiSelector(selectorId, changeIvaCallback = null) {
      const element = document.getElementById(selectorId);
      if (!element) {
        console.warn(`Elemento con id ${selectorId} no encontrado.`);
        return; // Salir de la función si el elemento no existe
      }

      // Intentar acceder al shadowRoot o continuar sin él
      const shadowRoot = element.shadowRoot || element; // Usar el elemento directamente si no hay shadowRoot
      const liElements = shadowRoot.querySelectorAll('ul li');
      const selectAllInput = shadowRoot.querySelector('#select-all');

      const handleClick = (e) => {
        if (changeIvaCallback) {
          changeIvaCallback();
        }
        e.stopPropagation();
        addFiltersBadge(element);
      };

      // Añadir listeners a los elementos <li> y al checkbox de selección general
      liElements.forEach((li) => {
        li.addEventListener('click', handleClick);
      });

      if (selectAllInput) {
        selectAllInput.addEventListener('click', handleClick);
      }
    }

    // Función para actualizar el estado del selector de IVA
    function onTaxCountryChange() {
      const taxCountry = document.getElementById("multiple-taxcountries");
      const vatRatesSelector = document.getElementById("multiple-vatrates");
      const taxCountryValue = taxCountry && taxCountry.dataset.value ? taxCountry.dataset.value.split(',').map(item => item.trim()) : [];

      debugLog("Selected taxCountryValue:", taxCountryValue);

      // Deshabilitar y limpiar el selector de IVA inicialmente
      vatRatesSelector.setAttribute("disabled", "disabled");
      vatRatesSelector.setAttribute("data-value", "");
      vatRatesSelector.value = '';
      vatRatesSelector.updateItems();

      // Si hay un país seleccionado, activar el selector y rellenar valores
      if (taxCountryValue.length > 0) {
        vatRatesSelector.removeAttribute("disabled");
        const vatRates = getVatRateValues(taxCountryValue);
        updateMultiCheckboxOptions(vatRatesSelector.id, vatRates);
      }
    }

    function getVatRateValues(taxCountryValues) {
      // Aseguramos que taxCountryValues sea un arreglo
      if (!Array.isArray(taxCountryValues)) {
        taxCountryValues = taxCountryValues.split(',').map(item => item.trim());
      }

      const vatRatesSet = new Set(); // Usamos un Set para evitar duplicados

      // Iteramos sobre los países seleccionados y obtenemos sus valores de IVA
      taxCountryValues.forEach(countryCode => {
        const vatRates = dj.value.vat_rates.find(rate => rate.country_code === countryCode);
        if (vatRates) {
          vatRates.vat_rates.split(',').forEach(rate => vatRatesSet.add(rate.trim()));
        }
      });

      // Convertimos el Set en un Array ordenado
      return Array.from(vatRatesSet).map(Number).sort((a, b) => a - b);
    }


    const updateMultiCheckboxOptions = (multiCheckboxId, ratesArray) => {
      const multiCheckbox = document.querySelector(`#${multiCheckboxId}`);
      if (multiCheckbox) {
        const ulElement = document.createElement('ul');
        ulElement.setAttribute('slot', 'check-values');
        ratesArray.forEach(rate => {
          const liElement = document.createElement('li');
          liElement.className = 'cursor-default';
          liElement.setAttribute('id', 'multi-value');
          liElement.setAttribute('value', rate);
          liElement.setAttribute('multi-title', rate + '%');
          ulElement.appendChild(liElement);

          const existingSlot = multiCheckbox.querySelector('ul[slot="check-values"]');
          if (existingSlot) {
              multiCheckbox.removeChild(existingSlot);
          }
          multiCheckbox.appendChild(ulElement);
        });
        multiCheckbox.updateItems();
        addListenerToMultiSelector(multiCheckboxId);
      }
    }

    // DYNAMIC HANDLING FOF MULTICHECK FILTERS

    // DYNAMIC HANDLING FOF MULTICHECK FILTERS
    const massiveOptionPerm = () =>{
      let perms = "{{ perms.users.is_superuserAPP }}";
      if (perms == "False") {
        $('#selectValue').empty().append("<option value='empty'>{% trans 'Selecciona una acción múltiple' %}</option><option value='download'>{% trans 'Descargar facturas' %}</option>");
      }
    }

    function checkCollapse() {
      const collapse = document.getElementById("collapse1");
      const incTable = document.getElementById("tableColapse1");
      const expTable = document.getElementById("tableColapse2");
      const resTable = document.getElementById("tableColapse3");

      // Verificar cada elemento y mostrar un mensaje específico si no existe
      if (!incTable) {
        console.warn(gettext('El elemento tableColapse1 no está presente en el DOM.'));
      }
      if (!expTable) {
        console.warn(gettext('El elemento tableColapse2 no está presente en el DOM.'));
      }
      if (!resTable) {
        console.warn(gettext('El elemento tableColapse3 no está presente en el DOM.'));
      }

      // Si alguno falta, detener la función
      if (!incTable || !expTable || !resTable) {
        return;
      }

      const expTableHeight = getExpenseDetailCardHeight(expTable) + "px";

      incTable.style.height = expTableHeight;
      resTable.style.height = expTableHeight;
    }

    function getExpenseDetailCardHeight(element) {
      if (!element) {
        console.warn(gettext('El elemento proporcionado a getExpenseDetailCardHeight no se encuentra en el DOM.'));
        return 0; // Devuelve una altura predeterminada en caso de que el elemento no exista
      }
      if (!element.parentElement) {
        console.warn(gettext('El elemento proporcionado tiene un padre inexistente en el DOM.'));
        return 0; // Devuelve una altura predeterminada en caso de que el padre no exista
      }
      const collapseElement = element.parentElement;
      const originalDisplay = collapseElement.style.display;
      collapseElement.style.display = 'block';

      const height = element.scrollHeight;
      collapseElement.style.display = originalDisplay;

      return height;
    }

    const dropdownFilterFormHack = () => {
      const dropdownFiltersForm = document.getElementById('dropdownFiltersForm');
      dropdownFiltersForm.style.opacity = '1';
      dropdownFiltersForm.style.display = 'none';
    }

    // Definir los nombres de los meses directamente desde Django
    const MONTH_NAMES = [
      '{% trans "ene" %}',
      '{% trans "feb" %}',
      '{% trans "mar" %}',
      '{% trans "abr" %}',
      '{% trans "may" %}',
      '{% trans "jun" %}',
      '{% trans "jul" %}',
      '{% trans "ago" %}',
      '{% trans "sep" %}',
      '{% trans "oct" %}',
      '{% trans "nov" %}',
      '{% trans "dic" %}'
    ];

  </script>
  <!-- JQUERY DATA TABLE  -->

  <!-- VUE3 JS  -->
  <script type="text/javascript">
    // IMPORTS /////////////////////////////////////////////////////////////////////////
    const {ref, watch} = Vue;

    // VARIABLES ///////////////////////////////////////////////////////////////////////
    const inputId = ref(null);
    const inputPaisIva = ref(null);
    const inputCategoria = ref(null);
    const inputFile = ref(null);
    const inputFilename = ref(null);
    const inputExportCSV = ref(false);
    const invoiceDataJSON = ref({});
    const dj = ref({});

    // METHODS or FUNCTIONS ////////////////////////////////////////////////////////////
    const getDjangoData = (djObj = null) => {
      try {
        if (!dj.value || dj.value == null || Object.keys(dj.value).length < 1) {
          djObj = JSON.parse(JSON.stringify(
            {{json | safe}}
          ));
        }
        if (djObj != null) {
          debugLog("djObj: ", djObj);
          let dj2 = {};
          for (const [key, value] of Object.entries(djObj)) {
            dj2[key] = [];
            for (const obj of JSON.parse(value)) {
              dj2[key].push({...obj?.fields, "pk": obj?.pk})
            }
          }
          dj2.seller = dj2?.seller?.length > 0 ? dj2.seller[0] : {};
          dj.value = dj2;
        }
      } catch (error) {
        console.error("Error in getDjango: ", error);
        dj.value = {};
      }
      debugLog(dj.value);
    };

    const getCountryNameByCode = (code) => {
      const country = dj.value.countries.filter(co => co.pk.toUpperCase() == code.toUpperCase())[0];
      const countryName = country?.name ? country?.name : `País ${code}`;
      return countryName;
    }

    const handleFiles = () => {
      const inputElement = document.getElementById("file");
      const fileList = inputElement.files;
      debugLog("fileList: ", fileList);
    }

    // Manejar el cambio de año y habilitar/deshabilitar el mes
    const onChangeYear = () => {
      const year = document.getElementById("year");
      const period = document.getElementById("multiple-month");
      if (year && year.value) {
        period.removeAttribute("disabled");  // Habilitar selector de meses
      } else {
        period.setAttribute("disabled", "disabled");  // Deshabilitar selector de meses
        period.value = '';  // Limpia el valor del selector
        addFiltersBadge(period);
      }
    }

    // INITIALIZE //////////////////////////////////////////////////////////////////////
    getDjangoData();

    // DATA EXPORT: ALL VARIABLES AND METHODS //////////////////////////////////////////
    const data_export = {
      dj,
      invoiceDataJSON,

      inputId,
      inputPaisIva,
      inputCategoria,
      inputFile,
      inputFilename,
      inputExportCSV,

      getCountryNameByCode,
    };

    // CREATE VUE 3 ////////////////////////////////////////////////////////////////////
    const createVue3 = (target, data_export, VUE3 = Vue) => {
      const {createApp} = VUE3;
      const {VGrid} = "vue3-datagrid";
      const app = createApp({
        components: {
          EasyDataTable: window["vue3-easy-data-table"],
        },
        delimiters: ['[[', ']]'],
        el: target,
        data() {
          return {...data_export}
        },
        methods: {
          getPosNegClass(value, sign){
            const numericValue = parseFloat(value) * sign;
            if (numericValue > 0) {
              return 'text-success';
            } else if (numericValue < 0) {
              return 'text-danger fw-bold';
            } else {
              return 'text-muted';
            }
          },
        }
      });
      // const vuetify = createVuetify();
      // app.use(vuetify)
      app.mount(target);
    };
    createVue3('.vue', data_export);
    createVue3('#toast', data_export);
    createVue3('#export', data_export);
  </script>

  <!-- VUE3 JS  -->
{% endblock javascripts %}
