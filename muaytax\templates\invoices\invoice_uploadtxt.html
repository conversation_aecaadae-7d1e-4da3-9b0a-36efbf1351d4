{% extends "layouts/base.html" %}
{% load static %}
{% load crispy_forms_tags %}
{% load utils %}
{% load i18n %}
{% block stylesheets %}
  <link rel="stylesheet" href="{{ STATIC_URL }}assets/css/plugins/style.css"/>
  <link rel="stylesheet" crossorigin href="{{ STATIC_URL }}assets\cdns_locals\css\all\v6.2.1\fontawesome-all.css"/>
  <style>
  .tooltip-inner a {
  color:white;
  text-decoration: none;
  }
  </style>
{% endblock stylesheets %}
{% block title %}
  {% trans "Lector de Facturas mediante TXT Amazon" %}
{% endblock title %}
{% block breadcrumb %}
  <div class="page-header">
    <div class="page-block">
      <div class="row align-items-center">
        <div class="col">
          <div class="page-header-title">
            <h5 class="m-b-10">
              <a href="javascript:history.back()"><i class="feather icon-arrow-left"></i></a> &nbsp;
              {% trans "Facturas: Lector de TXT Amazon" %}
            </h5>
          </div>
          <div class="row">
            <div class="col">
              <ul class="breadcrumb">
                {% if user.role == 'manager' %}
                  <li class="breadcrumb-item">
                    <a href="{% url 'home' %}"><i class="feather icon-home"></i></a>
                  </li>
                  <li class="breadcrumb-item">
                    <a href="{% url 'app_sellers:list' %}">{% trans "Vendedores" %}</a>
                  </li>
                  <li class="breadcrumb-item">
                    <a href="{% url 'app_sellers:summary' seller.shortname %}">
                      {% if seller.name is not None %}
                        {{ seller.name.capitalize }}
                      {% else %}
                        {% trans "Resumen Usuario" %}
                      {% endif %}
                    </a>
                  </li>
                  <li class="breadcrumb-item">
                    <a href="./upload">{% trans "Lector de TXT Amazon" %}</a>
                  </li>
                {% else %}
                  <li class="breadcrumb-item">
                    <a href="{% url 'home' %}"><i class="feather icon-home"></i></a>
                  </li>
                  <li class="breadcrumb-item">
                    <a href="{% url 'app_invoices:seller_invoices' seller.shortname %}">{% trans "Facturas" %}</a>
                  </li>
                  <li class="breadcrumb-item">
                    <a href="./upload">{% trans "Lector de TXT Amazon" %}</a>
                  </li>
                {% endif %}
              </ul>
            </div>
            {% comment %}
            <div class="col col-1">
              <ul class="breadcrumb justify-content-center">
                <li class="breadcrumb-item ">
                  <a href="./uploadtxt">Subir TXT Amazon</a>
                </li>
              </ul>
            </div>
            <div class="col col-1">
              <ul class="breadcrumb justify-content-center">
                <li class="breadcrumb-item ">
                  <a href="./upload">Subir Facturas</a>
                </li>
              </ul>
            </div>
            <div class="col col-1">
              <ul class="breadcrumb justify-content-center">
                <li class="breadcrumb-item ">
                  <a href="{% url 'app_invoices:seller_invoices_category' seller.shortname 'expenses' %}">
                    Facturas de Gasto
                  </a>
                </li>
              </ul>
            </div>
            <div class="col col-1">
              <ul class="breadcrumb justify-content-center">
                <li class="breadcrumb-item ">
                  <a href="{% url 'app_invoices:seller_invoices_category' seller.shortname 'sales' %}">
                    Facturas de Venta
                  </a>
                </li>
              </ul>
            </div>
            <div class="col col-1">
              <ul class="breadcrumb justify-content-center">
                <li class="breadcrumb-item">
                  <a href=".">Todas las Facturas</a>
                </li>
              </ul>
            </div>
            {% endcomment %}
          </div>
        </div>
        {% if user.role == 'manager' %}
          <div class="col-2 d-flex justify-content-end" style="padding: 0px 25px;">
            <a href="{% url 'app_importers:amz_list' seller.shortname %}" class="btn btn-primary">
              {% trans "Ver TXT Subidos" %}
            </a>
          </div>
        {% endif %}
      </div>
    </div>
  </div>
{% endblock breadcrumb %}
{% block content %}
  <div class="row">
    <!-- Cargar Documentos | START  -->
    <div class="col-12 my-0" id="uploadstxt">
      <div class="card my-0">
        <div class="card-header row">
          <div class="col-11 d-flex justify-content-start align-items-center text-left">
            <h5>{% trans "Lector de TXT Amazon" %}</h5>
          </div>
        </div>
        <div class="card-body border">
          {% comment %}
          <div class="alert alert-warning w-50 text-center mx-auto">
            <br />
            <h2> EN MANTENIMIENTO </h2>
            <h2><i class="fas fa-duotone fa-triangle-exclamation"></i></h2>
            <br />
            <h4> Temporalmente Deshabilitado por mantenimiento </h4>
            <br />
          </div>
          {% endcomment %}

          {% comment %}
          {% if user.role == 'seller' %}
            <div class="alert alert-warning w-50 text-center mx-auto">
              <br />
              <h2> FIN DE PERIODO </h2>
              <h2><i class="fas fa-duotone fa-triangle-exclamation"></i></h2>
              <br />
              <h4> Temporalmente Deshabilitado. </h4>
              <br />
            </div>
          {% else %}
          {% endcomment %}

          {% if error %}
            <div class="alert alert-danger w-25 text-center mx-auto">{{ error | safe }}</div>
          {% endif %}
          <form method="post" enctype="multipart/form-data"
                action="{% url 'app_invoices:seller_invoice_create_txt2' seller.shortname %}" id="form-uploadtxt"
                style="text-align:center;">
            {% csrf_token %}
            <div class="d-none d-print-none">
              <input
                type="hidden"
                id="id"
                name="{{ form_create.seller.name }}"
                value="{{ seller.pk }}"
              />
            </div>
            <p for="file">{% trans "*Solo válido para TXT de Amazon Europa. Contactad a soporte para otros reportes." %}</p><br>
            <div class="d-none d-print-none">
              <!-- change d-x-none to d-none -->
              <!-- change type text to hidden -->
              <input
                type="hidden"
                id="id"
                name="{{ form_create.seller.name }}"
                value="{{ seller.pk }}"
              />
              <input
                type="hidden"
                id="tax_country"
                name="tax_country"
                v-model="inputPaisIva"
              />
              <input
                type="hidden"
                id="invoice_category"
                name="invoice_category"
                v-model="inputCategoria"
              />
              <input
                type="hidden"
                id="invoice_type"
                name="invoice_type"
                v-model="inputCategoria"
              />
            </div>
            <div id="fallback" class="fallback">
              <input type="file" id="id_file" name="file" class=" form-control w-25 text-center mx-auto"/>
              <br><br><br>
              <input type="submit" id="submit-button" value="{% trans 'Continuar' %}" class="btn btn-primary" disabled>
            </div>
            <div id="spinner" style="display: none;">
              <div class="spinner-border m-3" role="status">
                <span class="sr-only">{% trans "Cargando..." %}</span>
              </div>
              <div>
                <h4>{% trans "Tu TXT se está procesando, por favor espere." %}</h4>
              </div>
            </div>
          </form>
          {% comment %} {% endif %} {% endcomment %}
        </div>
      </div>
    </div>
    <!-- Cargar Documentos | END  -->

    <!-- Documentos | START  -->
    {% if amz_txt_eur %}
      <div class="col-12 my-0">
        <div class="card user-profile-list">
          <div class="card-body">
            <div class="dt-responsive table-responsive">
              <table id="list-table" class="table nowrap">
                <thead>
                <tr>
                  <th>{% trans "Nombre Fichero" %}</th>
                  <th>{% trans "Amazon ID" %}</th>
                  <th>{% trans "Mes" %}</th>
                  <th>{% trans "Año" %}</th>
                  <th>{% trans "Fecha Carga" %}</th>
                  <th style="width:5%;">{% trans "Estado" %}</th>
                  <th style="width:5%;">{% trans "Acciones" %}</th>
                </tr>
                </thead>
                <tbody>
                {% for object in amz_txt_eur %}
                  <tr>
                    <td class="align-middle">
                      <span>{{ object.file }} </span>
                    </td>
                    <td class="align-middle">
                      <span>{{ object.amz_id }} </span>
                    </td>
                    <td class="align-middle">
                      <span>{{ object.month }} </span>
                    </td>
                    <td class="align-middle">
                      <span>{{ object.year }} </span>
                    </td>
                    <td class="align-middle">
                      <span>{{ object.created_at }}</span>
                    </td>
                    <td class="align-middle">
                      <h5 class="text-center">
                     <span data-bs-toggle="tooltip" data-bs-placement="top" data-bs-html="true"
                       {% if object.error_message != None %}
                           data-bs-title="{{ object.error_message }}"
                       {% endif %}
                       {% if object.error_message == None %}
                           data-bs-title="{% trans 'Sin información' %}"
                       {% endif %}
                     >
                    {% if object.status.code == "processed" %}
                      <i class="fa-regular fa-xl fa-circle-check" style="color: #02c018;"></i>
                    {% elif object.status.code == "failed" %}
                      <i class="fa-regular fa-xl fa-circle-xmark" style="color: #ff0000;"></i>
                    {% else %}
                      <i class="fa-regular fa-xl fa-circle-question" style="color: #ff9500;"></i>
                    {% endif %}
                    </span>
                      </h5>
                    </td>
                    <td class="align-middle">
                      <div>
                        <a class="btn btn-icon btn-info" href="{{ object.get_file_url }}" target="_blank" download>
                          <i class="fa-solid fa-download"></i>
                        </a>
                      </div>
                    </td>
                  </tr>
                {% endfor %}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    {% endif %}
    <!-- Documentos | END  -->
  </div>
{% endblock content %}
{% block javascripts %}
  <script src="{% static 'assets/js/plugins/simple-datatables.js' %}"></script>
  <script>
    const dataTableOptions = {
      paging: true,
      perPageSelect: [5, 10, 15, 20, 25, 50, 100], // Paginator Values
      perPage: 5, // Paginator Default Value
      searchable: true,
      sortable: true,
      truncatePager: true,
      header: true,
      footer: false,
      labels: {
        perPage: "{select}",
        noRows: "{% trans 'No se han encontrado TXT de Amazon (EUR)' %}",
        noResults: "{% trans 'No hay resultados que coincidan con su búsqueda.' %}",
        info: "<p>{% trans 'Mostrando {start} a {end} de {rows} resultados.' %}<p><p>{% trans 'Página {page} de {pages} páginas.' %}</p>"
      },
      layout: {
        top: "",
        bottom: "{info}{pager}"
      },
    };
    const dataTable = new simpleDatatables.DataTable("#list-table", dataTableOptions);

    const getFileName = (file) => {
      const fileName = file.split('\\').pop().split('/').pop();
      return fileName;
    }
  </script>
  <script>
    const form = document.getElementById('form-uploadtxt');
    const submitButton = document.getElementById('submit-button');
    const spinner = document.getElementById('spinner');
    const fileInput = document.getElementById('id_file');

    form.addEventListener('submit', async (e) => {
      e.preventDefault(); // Prevenir el envío normal del formulario
      submitButton.disabled = true;
      document.getElementById("fallback").style.display = 'none';
      spinner.style.display = 'block';

      try {
        const formData = new FormData(form);
        const response = await fetch(form.action, {
          method: 'POST',
          body: formData,
          headers: {
            'X-Requested-With': 'XMLHttpRequest'
          }
        });

        const data = await response.json();

        if (response.ok) {
          // Si la subida fue exitosa, redirigir
          window.location.href = form.action;
        } else if (response.status === 403 && data.swal) {
          // Mostrar el mensaje de error con SweetAlert2
          await Swal.fire({
            title: data.swal.title,
            text: data.swal.text,
            icon: data.swal.icon,
            confirmButtonText: data.swal.confirmButtonText,
            confirmButtonColor: data.swal.confirmButtonColor
          });
          // Restaurar el formulario
          submitButton.disabled = false;
          document.getElementById("fallback").style.display = 'block';
          spinner.style.display = 'none';
        } else {
          // Otros errores
          throw new Error(data.error || '{% trans "Error al procesar el archivo" %}');
        }
      } catch (error) {
        // Mostrar error genérico
        await Swal.fire({
          title: '{% trans "Error" %}',
          text: error.message || '{% trans "Error al procesar el archivo" %}',
          icon: 'error',
          confirmButtonText: '{% trans "Entendido" %}'
        });
        // Restaurar el formulario
        submitButton.disabled = false;
        document.getElementById("fallback").style.display = 'block';
        spinner.style.display = 'none';
      }
    });

    fileInput.addEventListener('change', () => {
      if (fileInput.files.length > 0 && fileInput.files[0].name.endsWith('.txt')) {
        submitButton.disabled = false;
      } else {
        fileInput.value = ''
        submitButton.disabled = true;
        Swal.fire({
          title: '{% trans "Formato no válido" %}',
          text: '{% trans "El archivo debe ser un TXT de Amazon" %}',
          icon: 'error',
          confirmButtonText: '{% trans "Entendido" %}'
        });
      }
    });
  </script>
{% endblock javascripts %}
