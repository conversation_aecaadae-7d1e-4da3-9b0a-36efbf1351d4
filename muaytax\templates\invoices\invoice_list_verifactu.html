{% extends "layouts/base.html" %}
{% load static crispy_forms_tags utils %}
{% load crispy_forms_tags %}
{% load i18n %}

{% block title %}
  {% trans "Facturas Verifactu" %}
{% endblock title %}

{% block stylesheets %}
  <link rel="stylesheet" href="{% static 'assets/css/plugins/style.css' %}"/>
  <link rel="stylesheet" crossorigin href="{{ STATIC_URL }}assets\cdns_locals\css\all\v6.2.1\fontawesome-all.css" type="text/css" />
  <link rel="stylesheet" href="{{ STATIC_URL }}assets/cdns_locals/css/jquery/jquery.dataTables.min-v1.11.5.css">

  <!-- Iconos -->
  <link rel="stylesheet" crossorigin href="{{ STATIC_URL }}assets/cdns_locals/css/all/v6.2.1/fontawesome-all.css" type="text/css" />
  <link rel="stylesheet" href="{% static 'assets/fonts/material/css/materialdesignicons.min.css' %}">

  <!-- DataTables (elige solo una versión de core) -->
  <link rel="stylesheet" href="{{ STATIC_URL }}assets/cdns_locals/css/jquery/jquery.dataTables.min-v1.11.5.css">
  <link rel="stylesheet" crossorigin href="{{ STATIC_URL }}assets/cdns_locals/css/dataTables/dataTables.bootstrap5.min-v2.0.8.css" type="text/css">
  <link rel="stylesheet" crossorigin href="{{ STATIC_URL }}assets/cdns_locals/css/select/select.dataTables.min-v1.6.2.css" type="text/css">
  <link rel="stylesheet" href="{{ STATIC_URL }}assets/cdns_locals/css/dataTables/dataTables.dataTables-v2.0.8.css" />

  <!-- Scripts -->
  <script type="module" src="{{ STATIC_URL }}assets/js/plugins/multi-checkbox.js"></script>

  <!-- Limit Characters in Table Span -->
  <style>
    #list-table td span {
      display:inline-block;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 50vw;
    }
    .table-head {
          position: sticky;
          top: 0;
          background-color: #f2f2f2;
          z-index: 1;
    }


    {% comment %} /* multiselectors style */
    multi-status,
    multi-operationverifactu,
    multi-statusverifactu,
    multi-month {
      /* Element */
      --mc-z-index: 8 !important;
      --mc-cont-z-index: 20 !important;
      --mc-border: 1px solid #ced4da;;
      --mc-display: inline-block;
      --mc-font: 14px !important;
      --mc-margin: 0;
      --mc-vertical-align: middle;
      /* Dropdown */
      --mc-dropdown-background: #fff;
      --mc-dropdown-box-shadow: 0 1px 2px 0 rgba(57, 70, 92, .05);
      --mc-dropdown-max-height: 350px;
      --mc-dropdown-text-align: left;
      --mc-dropdown-width: 100%;
      /* Input */
      --mc-target-background: #fff;
      --mc-target-color: #000000;
      --mc-target-cursor: pointer;
      --mc-target-margin: 0;
      --mc-target-padding: 0px 0px 0px 3px;
      --mc-target-outline: none;
      --mc-vertical-align: middle;
      --mc-target-width: 268px;
      /* Dropdown Line Items */
      --mc-dropdown-line-height: 2em;
      --mc-ul-margin: 0;
      --mc-ul-padding: 0;
      --mc-checkbox-height: 30px;
      --mc-checkbox-width: 20px;
      --mc-li-cursor: pointer;
      /* Toggle Button */
      --mc-toggle-button-background: #ffffff;
      --mc-toggle-button-color: #000000;
      --mc-toggle-button-cursor: pointer;
      --mc-toggle-button-height: 46px;
      --mc-toggle-button-outline: none;
      --mc-toggle-button-width: 23px;
      /* Close Button */
      --mc-close-button-background: #efefef;
      --mc-close-button-border: none;
      --mc-close-button-border-radius: default;
      --mc-close-button-color: #000000;
      --mc-close-button-cursor: pointer;
      --mc-close-button-display: block;
      --mc-close-button-height: 22px;
      --mc-close-button-margin: 5px auto;
      --mc-close-button-outline: none;
      --mc-close-button-width: 22px;
    }
    #multi-value:hover {
      background-color: #0d6efd;
      --mc-dropdown-color: #fff;
    }
    #multi-value:hover div {
      color: #fff;
    } {% endcomment %}

    .list-action-btn-block{
      position: relative;
    }
    .list-action-btn-block > *{
      font-size: 12px;
      margin-bottom: unset!important;
      margin-right: unset!important;
    }
    .list-action-btn-block button{
      min-width: max-content;
    }

    {% comment %} .dropdown-form {
      display: block;
      min-width: 500px;
      position: absolute;
      background-color: white;
      border: 1px solid #ccc;
      padding: 10px;
      z-index: 1000;
      transform: translate(-70%, 10px);
      opacity: 0;
      transition: opacity 0.3s ease, transform 0.3s ease;
    }
    .btn.dropdown-toggle:after {
      display: none
    }

    .top-right-badge{
      position: absolute!important;
      top: -10px!important;
      right: -5px!important;
      width: 25px!important;
      height: 25px!important;
      align-items: center;
      display: flex;
      justify-content: center;
    } {% endcomment %}
    .search-container {
      display: inline-block;
      position: relative;
      width: 200px;
      transition: width 0.4s ease-in-out;
    }
    .search-container > input {
      font-size: 12px;
    }
    .search-container:focus-within {
      width: 100%; /* Expanded width */
    }

    #list-table_length {
      display: none;
    }

    #list-table.table-hover tbody tr:hover {
    background: #d6d6d6d2!important;
  }


  .placeholder{
    background-color: transparent;
  }

  .select-disabled{
     pointer-events: none; /* Desactiva interacciones */
    cursor: default;
    background-color: #f2f2f2;
    border-color: #ced4da;
    color: #6c757d;
    opacity: 0.65;
  }

  .pdf-object{
    width: 100%;
    height: 100%;

  }

  .modal-xl {
      max-width: 1500px;
  }

  .btn-group.dropstart .dropdown-menu::before,
  .btn-group.dropstart .dropdown-menu::after {
    display: none !important;
  }

  .dropstart .dropdown-toggle::before {
    display: none;
  }



  </style>
{% endblock stylesheets %}

{% block breadcrumb %}
  <div class="page-header">
    <div class="page-block">
      <div class="row align-items-center">
        <div class="col">
          <div class="page-header-title">
            <h5 class="m-b-10">{% trans "Facturas Verifactu" %}</h5>
          </div>
          <ul class="breadcrumb">
            <li class="breadcrumb-item">
              <a href="{% url 'home' %}"><i class="feather icon-home"></i></a>
            </li>
            <li class="breadcrumb-item">
              <a href="{% url 'app_sellers:list' %}">{% trans "Vendedores" %}</a>
            </li>
            <li class="breadcrumb-item">
              <a href="{% url 'app_sellers:summary' seller.shortname  %}"> {{seller.name|title}} </a>
            </li>
            <li class="breadcrumb-item">
              <a href=".">{% trans "Facturas Verifactu" %}</a>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
{% endblock breadcrumb %}

{% block content %}
  <div class="row">
    <div class="col-lg-12">
      <div class="card user-profile-list">
        <div class="card-body">

        <!-- cartas con valores totales no es necesario-->
        <div class="col-12">
            <div class="row">
                <div class="col-12 col-sm-6 col-md-4 col-lg-3 no-wrap-with-fit-content">
                    <div class="card rounded-3 border">
                        <div class="card-block">
                            <div class="row d-flex align-items-center">
                                <div class="col">
                                    <h6><b>{% trans "TOTAL FACTURAS" %} </b></h6>
                                    <h3 class="f-w-300 d-flex align-items-center mb-2 text-muted">
                                      <b id="total_verifactu_invoices">&nbsp</b></i>
                                    </h3>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-12 col-sm-6 col-md-4 col-lg-3 no-wrap-with-fit-content">
                    <div class="card rounded-3 border">
                        <div class="card-block">
                            <div class="row d-flex align-items-center">
                                <div class="col">
                                    <h6><b>{% trans "TOTAL FACTURAS ENVIADAS" %}</b></h6>
                                    <h3 class="f-w-300 d-flex align-items-center mb-2 text-muted">
                                      <b id="correct_invoices_count">&nbsp</b>&nbsp&nbsp<i class="fa-solid fa-check" style="color: #02c018;"></i>
                                    </h3>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-12 col-sm-6 col-md-4 col-lg-3 no-wrap-with-fit-content">
                    <div class="card rounded-3 border">
                        <div class="card-block">
                            <div class="row d-flex align-items-center">
                                <div class="col">
                                    <h6><b>{% trans "TOTAL FACTURAS ACEPTADAS CON ERRORES" %}</b></h6>
                                    <h3 class="f-w-300 d-flex align-items-center mb-2 text-muted">
                                      <b id="correct_with_errors_invoices_count">&nbsp</b>&nbsp&nbsp
                                        <i class="fa-solid fa-check" style="color: #02c018;"></i>
                                        <i class="fa-solid fa-triangle-exclamation" style="color: #ffc107;" ></i>
                                    </h3>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-12 col-sm-6 col-md-4 col-lg-3  no-wrap-with-fit-content">
                    <div class="card rounded-3 border">
                        <div class="card-block">
                            <div class="row d-flex align-items-center">
                                <div class="col">
                                    <h6><b>{% trans "TOTAL FACTURAS NO ENVIADAS" %}</b></h6>
                                    <h3 class="f-w-300 d-flex align-items-center mb-2 text-muted">
                                        <b id="total_not_sent">&nbsp</b>&nbsp&nbsp<i class="fa-solid fa-xmark" style="color: #dc3545;"></i>
                                    </h3>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- search input -->
        <div class="col-12 mb-3">
            <div class="row">
                <div class="col-12 col-lg-6 d-flex justify-content-start filters-row">
                    <div class="col-12 col-lg-8 me-3">
                        <div class="input-group">
                            <input class="form-control fix-borders" type="search" id="search" name="search" placeholder="{% trans 'Presiona enter para buscar...' %}">
                            <span class="mdi mdi-magnify search-icon"></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
          <div class="card rounded">
            <div class="card-body">
              <div class="row mb-4">
                <div class="col-12 mb-3">
                  <div class="row gy-3 d-flex justify-content-between">
                    <div class="col">
                      <div class="d-flex align-items-center gap-2 justify-content-end">
                        <!-- Filter Section starts -->
                        <div class="gap-3 col-sm-12 col-lg-6 d-flex flex-column flex-lg-row justify-content-start justify-content-lg-start filters-row min-width-fit-content flex-fill">
                            <div class="d-flex flex-nowrap">
                              <select role="button" class="form-select form-control btn-light border h-100 select-enabled" id="status">
                                <option value="" disabled selected hidden> {% trans "Estado Factura" %} &nbsp;&nbsp;&nbsp;</option>
                                <option value="" class="placeholder"> {% trans "Todos estados" %} &nbsp;&nbsp;&nbsp;</option>
                                {% for status in invoice_statuses %}
                                  <option value="{{ status.pk }}">{{ status.description }} &nbsp;&nbsp;&nbsp;</option>
                                {% endfor %}
                                </select>
                            </div>
                            <div class="d-flex flex-nowrap">
                              <select role="button" class="form-select form-control btn-light border h-100 select-enabled" id="status_in_verifactu">
                                <option value="" disabled selected hidden> {% trans "Estado en Verifactu" %} &nbsp;&nbsp;&nbsp;</option>
                                <option value="" class="placeholder"> {% trans "Todos estado en Verifactu" %}&nbsp;&nbsp;&nbsp;</option>
                                <option value="Correcto">{% trans "Correcto" %} &nbsp;&nbsp;&nbsp;</option>
                                <option value="AceptadoConErrores">{% trans "Aceptada con errores" %} &nbsp;&nbsp;&nbsp;</option>
                                <option value="not_sent">{% trans "No enviada a verifactu" %} &nbsp;&nbsp;&nbsp;</option>
                              </select>
                            </div>

                            <div class="d-flex flex-nowrap">
                              <select role="button" class="form-select form-control btn-light border h-100 select-enabled" id="operation_type">
                                <option value="" disabled selected hidden> {% trans "Tipo operación en Verifactu" %} &nbsp;&nbsp;&nbsp;</option>
                                <option value="" class="placeholder">{% trans "Todas operaciones Verifactu" %} &nbsp;&nbsp;&nbsp;</option>
                                <option value="Alta">{% trans "Alta" %} &nbsp;&nbsp;&nbsp;</option>
                                <option value="Anulacion">{% trans "Anulación" %} &nbsp;&nbsp;&nbsp;</option>
                                <option value="no_info">{% trans "Sin información de operación" %} &nbsp;&nbsp;&nbsp;</option>
                              </select>
                            </div>

                            <!-- year selector filter -->
                            <div class="d-flex flex-nowrap">
                              <select class="form-select form-select-sm form-control btn-light month-select rounded-0 rounded-end select-enabled" id="period">
                                <option value="" disabled selected hidden>{% trans "Periodo" %}</option>
                                <option value="Q1">{% trans "Trimestre 1" %} &nbsp;&nbsp;&nbsp;</option>
                                <option value="Q2">{% trans "Trimestre 2" %} &nbsp;&nbsp;&nbsp;</option>
                                <option value="Q3">{% trans "Trimestre 3" %} &nbsp;&nbsp;&nbsp;</option>
                                <option value="Q4">{% trans "Trimestre 4" %} &nbsp;&nbsp;&nbsp;</option>
                                <option value="0A">{% trans "Anual" %} &nbsp;&nbsp;&nbsp;</option>
                                <option value="M1">{% trans "Enero" %} &nbsp;&nbsp;&nbsp;</option>
                                <option value="M2">{% trans "Febrero" %} &nbsp;&nbsp;&nbsp;</option>
                                <option value="M3">{% trans "Marzo" %} &nbsp;&nbsp;&nbsp;</option>
                                <option value="M4">{% trans "Abril" %} &nbsp;&nbsp;&nbsp;</option>
                                <option value="M5">{% trans "Mayo" %} &nbsp;&nbsp;&nbsp;</option>
                                <option value="M6">{% trans "Junio" %} &nbsp;&nbsp;&nbsp;</option>
                                <option value="M7">{% trans "Julio" %} &nbsp;&nbsp;&nbsp;</option>
                                <option value="M8">{% trans "Agosto" %} &nbsp;&nbsp;&nbsp;</option>
                                <option value="M9">{% trans "Septiembre" %} &nbsp;&nbsp;&nbsp;</option>
                                <option value="M10">{% trans "Octubre" %} &nbsp;&nbsp;&nbsp;</option>
                                <option value="M11">{% trans "Noviembre" %} &nbsp;&nbsp;&nbsp;</option>
                                <option value="M12">{% trans "Diciembre" %} &nbsp;&nbsp;&nbsp;</option>
                              </select>
                            </div>

                            <!-- year selector filter -->
                            <div class="d-flex flex-nowrap">
                              <select role="button" class="form-select form-control btn-light border h-100 select-enabled" name="year-input" id="year">
                                  <option value="" disabled selected hidden> {% trans "Año" %} &nbsp;&nbsp;&nbsp;</option>
                                  <option value="">{% trans "Todos los años" %} &nbsp;&nbsp;&nbsp;</option>
                                  <option value="2022">2022 &nbsp;&nbsp;&nbsp;</option>
                                  <option value="2023">2023 &nbsp;&nbsp;&nbsp;</option>
                                  <option value="2024">2024 &nbsp;&nbsp;&nbsp;</option>
                                  <option value="2025">2025 &nbsp;&nbsp;&nbsp;</option>
                              </select>
                            </div>

                            <div class="d-flex gap-3 flex-nowrap justify-content-end col">
                              <button type="button" class="btn btn-light" onclick="cleanFilters()" style="margin:0;" id="cleanButton" data-bs-toggle="tooltip" data-bs-placement="top" title="{% trans 'Limpiar filtros' %}">
                                <span>
                                  <i class="fa-solid fa-xl fa-eraser"></i>
                                </span>
                              </button>
                              <button type="button" id="applyFiltersButton" class="btn btn-dark" onclick="filter()" style="margin:0;" data-bs-toggle="tooltip" data-bs-placement="top" title="{% trans 'Aplicar filtros' %}">
                                  <span id="apply"><i class="fa-solid fa-xl fa-magnifying-glass"></i></span>
                                  <span id="spinner-apply" style="display:none;">
                                  <div class="spinner-border" role="status" style="width: 15px; height: 15px; margin-left: 3px; ">
                                    <span class="sr-only">{% trans "Loading..." %}</span>
                                  </div>
                                  </span>
                              </button>
                            </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="dt-responsive table-responsive">
                <table id="list-table" class="table table-striped table-hover nowrap" style="width:100%">
                  <thead class="table-head">
                    <tr>
                      <th >{% trans "ID Factura" %}</th>
                      <th >{% trans "Estado" %}</th>
                      <th >{% trans "Número" %}</th>
                      <th >{% trans "Fecha" %}</th>
                      <th >{% trans "Estado en Verifactu" %}</th>
                      <th >{% trans "Tipo Operación" %}</th>
                      <th >{% trans "Fecha Op. Verifactu" %}</th>
                      <th >{% trans "Cancelada por gestor" %}</th>
                      <th >{% trans "Acciones" %}</th>
                    </tr>
                  </thead>
                  <tbody>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Modal con las opciones manuales de veriFactu -->
    {% include "invoices/include/modal/modal_verifactu_actions_list.html" %}
  <!-- Modal con las opciones manuales de veriFactu -->


{% endblock content %}
{% block javascripts %}
<link rel="stylesheet" href="{{ STATIC_URL }}assets/cdns_locals/css/sweetalert/sweetalert2.min-v11.1.4.css">
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/sweetalert/sweetalert2.min-v11.1.4.js"></script>
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery-3.6.0.min-v3.6.0.js"></script>
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/jquery/jquery.dataTables.min-v1.11.5.js"></script>
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/dataTables/dataTables.select.min-v1.6.2.js"></script>
  <script src="{{ STATIC_URL }}assets/cdns_locals/js/dataTables/dataTables.fixedHeader.min-v3.4.0.js"></script>
  <script src="{% static 'assets/js/loading.js' %}"></script>

  <!-- sweet alert Js -->
  <script src="{% static 'assets/js/plugins/sweetalert2.all.min.js' %}"></script>

  <script>
    // variables globales
    let shortname = "{{ seller.shortname }}";

  </script>

  <!-- JAVASCRIPT FACTURACIÓN DIGITAL -->
  <script type="text/javascript" src="{{ STATIC_URL }}assets/js/invoice/verifactu_list.js"></script>

{% endblock javascripts %}