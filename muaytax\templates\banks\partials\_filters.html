{% load i18n %}
<!-- <PERSON><PERSON> de filtros usando <PERSON> grid -->
<div class="row g-3 mb-3">
    <!-- Selector de cuentas -->
    <div class="col-md-3">
        <label for="account-dropdown-button" class="form-label">{% trans "Cuentas" %}</label>
        <div class="account-dropdown">
            <button id="account-dropdown-button" class="btn btn-outline-secondary account-dropdown-toggle w-100 d-flex justify-content-between align-items-center">
                <span id="account-selection-summary">{% trans "Seleccionar cuentas" %}</span> <i class="fas fa-chevron-down"></i>
            </button>
            <div id="account-dropdown-menu" class="account-checkbox-list dropdown-menu w-100">
                <div class="p-2">
                    <div class="d-flex justify-content-between mb-2">
                        <button id="select-all-accounts" class="btn btn-sm btn-primary">{% trans "Seleccionar todo" %}</button>
                        <button id="deselect-all-accounts" class="btn btn-sm btn-outline-secondary">{% trans "Deseleccionar todo" %}</button>
                    </div>
                    {% for account in accounts %}
                    <div class="form-check account-checkbox-item">
                        <input type="checkbox" class="form-check-input account-checkbox" id="account-{{ account.code }}" value="{{ account.code }}">
                        <label class="form-check-label" for="account-{{ account.code }}">{{ account.code }} - {{ account.translated_description }}</label>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>

    <!-- Selector de fecha -->
    <div class="col-md-3">
        <label for="date-range" class="form-label">{% trans "Rango de fechas" %}</label>
        <div class="input-group">
            <span class="input-group-text"><i class="far fa-calendar-alt"></i></span>
            <input type="text" id="date-range" class="form-control date-range-input" placeholder="{% trans 'Seleccionar fechas' %}">
        </div>
    </div>

    <!-- Botones de acción -->
    <div class="col-md-2 d-flex align-items-end">
        <button type="button" id="search-button" class="btn btn-primary w-100"><i class="fas fa-search"></i> {% trans "Buscar" %}</button>
    </div>

    <!-- Botón de exportación -->
    <div class="col-md-2 d-flex align-items-end">
        <button type="button" id="export-csv" class="btn btn-success w-100"><i class="fas fa-file-csv"></i> {% trans "Exportar CSV" %}</button>
    </div>

    <!-- Botón para desplegar filtros avanzados -->
    <div class="col-md-2 d-flex align-items-end">
        <button class="btn btn-outline-secondary w-100" type="button" id="toggle-advanced-filters">
            <i class="fas fa-filter"></i> {% trans "Filtros avanzados" %}
        </button>
    </div>
</div>

<!-- Sección de filtros avanzados colapsable -->
<div id="advancedFiltersCollapse" class="mb-3" style="display: none;">
    <div class="card">
        <div class="card-body">
            <div class="row g-3">
                <!-- Filtro de texto -->
                <div class="col-md-5">
                    <label class="form-label">{% trans "Filtrar por texto:" %}</label>
                    <div class="input-group">
                        <select class="form-select flex-grow-0" id="text-filter-type" style="width: auto;">
                            <option value="">{% trans "Tipo" %}</option>
                            <option value="+">{% trans "+ Contiene" %}</option>
                            <option value="-">{% trans "- No contiene" %}</option>
                        </select>
                        <input type="text" id="text-filter-value" class="form-control" placeholder="{% trans 'Texto a buscar' %}" disabled>
                        <select class="form-select flex-grow-0" id="text-filter-column" style="width: auto;" disabled>
                            <option value="all">{% trans "En Todas" %}</option>
                            <option value="date">{% trans "En Fecha" %}</option>
                            <option value="num">{% trans "En Nº Asiento" %}</option>
                            <option value="document">{% trans "En Documento" %}</option>
                            <option value="concept">{% trans "En Concepto" %}</option>
                        </select>
                    </div>
                </div>

                <!-- Filtro de cantidad -->
                <div class="col-md-5">
                    <label class="form-label">{% trans "Filtrar por cantidad:" %}</label>
                    <div class="input-group">
                        <select class="form-select flex-grow-0" id="amount-filter-type" style="width: auto;">
                            <option value="">{% trans "Tipo" %}</option>
                            <option value=">">{% trans "Mayor que" %}</option>
                            <option value="<">{% trans "Menor que" %}</option>
                        </select>
                        <input type="number" id="amount-filter-value" class="form-control" placeholder="{% trans 'Valor' %}" step="0.01" min="0" disabled>
                        <select class="form-select flex-grow-0" id="amount-filter-column" style="width: auto;" disabled>
                            <option value="debit">{% trans "En Debe" %}</option>
                            <option value="credit">{% trans "En Haber" %}</option>
                            <option value="balance">{% trans "En Saldo" %}</option>
                        </select>
                    </div>
                </div>

                <!-- Botón de toggle filtros -->
                <div class="col-md-2 d-flex align-items-end justify-content-center">
                    <button type="button" id="toggle-filters" class="btn btn-outline-success">
                        <i class="fas fa-toggle-off me-1"></i> {% trans "Filtro DESACTIVADO" %}
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
