import copy
import decimal
import j<PERSON>
import random
import time
import traceback
from datetime import date as dt
from datetime import datetime, timedelta

import numpy as np
import pandas as pd
import requests
from babel.numbers import format_decimal
from django.contrib.auth import get_user_model, update_session_auth_hash
from django.contrib.auth.forms import PasswordChangeForm
from django.contrib.auth.mixins import LoginRequiredMixin
from django.contrib.messages.views import SuccessMessageMixin
from django.core.cache import cache
from django.db.models import Case, DecimalField, F, Q, Sum, When
from django.db.models.functions import TruncMonth, TruncWeek
from django.http import HttpResponse, HttpResponseRedirect
from django.shortcuts import redirect
from django.urls import reverse
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django.views import View
from django.views.generic import DetailView, RedirectView, TemplateView, UpdateView
from django.utils import timezone
from django.core.cache import cache
from django.utils import translation
from django.shortcuts import redirect
from django.views.decorators.http import require_POST
from django.conf import settings

from muaytax.app_customers.models.customer import Customer
from muaytax.app_invoices.models.concept import Concept
from muaytax.app_invoices.models.invoice import Invoice
from muaytax.app_partners.models.partner import Partner
from muaytax.app_providers.models.provider import Provider
from muaytax.app_sellers.forms.seller import (
    sellerAddressChangeForm,
    sellerBankChangeForm,
    sellerChangeForm,
)
from muaytax.app_sellers.models.seller import Seller
from muaytax.app_sellers.models.seller_process import SellerProcess
from muaytax.app_sellers.models.seller_vat import SellerVat
from muaytax.app_sellers.models.seller_vat_activity import SellerVatActivity
from muaytax.app_tasks.models import TaskPending
from muaytax.app_workers.models.worker import Worker
from muaytax.tasks import check_48h_emails
from muaytax.users.forms import UserChangeForm
from muaytax.users.permissions import IsSellerRolePermission, IsUserUsernamePermission

User = get_user_model()


class HomeView(LoginRequiredMixin, TemplateView):

    def get(self, request):
        # ejecuta tareas a cada peticion
        # process_emails()
        check_48h_emails()

        if request.user.has_permission_manager():
            # return HttpResponseRedirect(reverse("app_sellers:list"))
            return HttpResponseRedirect(reverse("app_sellers:hub"))
        else:
            year = None
            month = None
            iae = None
            seller = Seller.objects.get(user=request.user)
            seller_vat_ids = SellerVat.objects.filter(seller=seller)
            seller_vat_activity = SellerVatActivity.objects.filter(sellervat__in=seller_vat_ids).order_by('sellervat_activity_iae__description').distinct('sellervat_activity_iae__description')

            iae = None
            if seller_vat_activity.count() == 1:
                iae_activity = seller_vat_activity.first()
                iae_code = iae_activity.sellervat_activity_iae.code

                # Si el código contiene un guion, quedarse con la parte después del guion
                if '-' in iae_code:
                    iae_code = iae_code.split('-')[-1]

                iae = iae_code
            else:
                iae = "all"

            try:
                # Obtener fecha actual
                date_now = datetime.now()
                # Obtener año actual
                year = str(date_now.year)
                # Obtener mes actual
                month = str(date_now.month).zfill(2)
            except:
                year = "all"
                month = "all"

            return HttpResponseRedirect(
                reverse("users:user_dash_filter", kwargs={"year": year, "month": month, "country": "all", "iae": iae}))

class UserDashFilterOld(LoginRequiredMixin, IsSellerRolePermission, TemplateView):
    model = User
    template_name = "home/index.html"

    def get_context_data(self, **kwargs):

        context = super().get_context_data(**kwargs)
        user = self.request.user
        seller = user.seller
        country = str(self.kwargs["country"]).strip()

        # Agregar el campo 'affiliate_program' al contexto
        context['affiliate_program'] = seller.affiliate_program

        # Obtener conteo de tareas pendientes no vistas
        pending_tasks_count = TaskPending.objects.filter(user=user, completed=False).count()
        context['pending_tasks_count'] = pending_tasks_count
        alert_pending_notification = False
        if pending_tasks_count > 0:
            alert_pending_notification = True

        context['alert_pending_notification'] = alert_pending_notification

        iaeSelected = 'all'
        if "iae" in self.kwargs and self.kwargs["iae"]:
            iaeSelected = str(self.kwargs["iae"]).strip()
        year = str(self.kwargs["year"]).strip()
        month = str(self.kwargs["month"]).strip()

        # print("country: " + country)
        # print("iaeSelected: " + iaeSelected)
        # print("year: " + year)
        # print("month: " + month)

        submitForm5472 = self.request.session.get('submit5472', None)
        submitFormBE15 = self.request.session.get('submitBE15', None)
        submitSellerRapForm = self.request.session.get('submitSellerRapForm', None)


        if submitForm5472:
            context['submitForm5472'] = submitForm5472
            del self.request.session['submit5472']

        if submitFormBE15:
            context['submitFormBE15'] = submitFormBE15
            del self.request.session['submitBE15']

        if submitSellerRapForm:
            context['submitSellerRapForm'] = submitSellerRapForm
            del self.request.session['submitSellerRapForm']




        seller_vats = SellerVat.objects.filter(seller=seller)
        seller_vat_activity = SellerVatActivity.objects.filter(sellervat__in=seller_vats).order_by('sellervat_activity_iae__description').distinct('sellervat_activity_iae__description')

        iae_activities = []
        if seller_vat_activity.count() > 1:
            for activity in seller_vat_activity:
                # Crear una copia del objeto relacionado para modificar
                modified_iae = copy.deepcopy(activity.sellervat_activity_iae)

                # Si el código contiene un guion, quedarse con la parte después del guion
                if '-' in modified_iae.code:
                    # Dividir el código en el guion y quedarse con la parte después del guion
                    modified_iae.code = modified_iae.code.split('-')[-1]

                iae_activities.append(modified_iae)


        context['iae'] = iae_activities
        context['seller_vat'] = SellerVat.objects.all().filter(seller_id=seller.id)
        context['seller_vat_count'] = SellerVat.objects.all().filter(seller_id=seller.id).count()
        context['customers_count'] = Customer.objects.all().filter(seller_id=seller.id).count()
        context['providers_count'] = Provider.objects.all().filter(seller_id=seller.id).count()
        context['partners_count'] = Partner.objects.all().filter(seller_id=seller.id).count()
        context['workers_count'] = Worker.objects.all().filter(seller_id=seller.id).count()
        context['invoices_count'] = Invoice.objects.all().filter(seller_id=seller.id).count()

        context['formUser'] = UserChangeForm(instance=user)
        context['formSeller'] = sellerChangeForm(instance=seller)
        context['formPassword'] = PasswordChangeForm(user=user)

        context['message_184'] = self.request.session.pop('message_184', None)

        invoices = Invoice.objects.filter(seller_id=seller.id)

        # Exclude Amazon Invoices Generated by User
        invoices = invoices.exclude(is_generated_amz=True)

        # Exclude Transfers
        invoices = invoices.exclude(transaction_type__code__icontains='-transfer')
        invoicesyear = invoices

        context['filter_year'] = "all"
        context['filter_month'] = "all"
        context['filter_country'] = "all"
        context['filter_iae'] = "all"

        if year != "all":
            invoices = invoices.filter(accounting_date__year=year)
            invoicesyear = invoices
            context['filter_year'] = year
        if month != "all":
            if month == "tri1":
                invoices = invoices.filter(accounting_date__month__in=[1, 2, 3])
            elif month == "tri2":
                invoices = invoices.filter(accounting_date__month__in=[4, 5, 6])
            elif month == "tri3":
                invoices = invoices.filter(accounting_date__month__in=[7, 8, 9])
            elif month == "tri4":
                invoices = invoices.filter(accounting_date__month__in=[10, 11, 12])
            else:
                invoices = invoices.filter(accounting_date__month=month)
            context['filter_month'] = month
        if country != "all":
            invoices = invoices.filter(tax_country__iso_code=country)
            context['filter_country'] = country

        if iaeSelected != "all":
            invoices = invoices.filter(iae__code__endswith=iaeSelected)
            context['filter_iae'] = iaeSelected

        all_concepts_seller = Concept.objects.filter(invoice__in=invoices)
        all_concepts_seller_yearly = Concept.objects.filter(invoice__in=invoicesyear)

        ingresostotalbase = 0
        ingresostotaliva = 0
        ingresostotalirpf = 0
        marketplace_data = []
        providers_data = []
        results_data = []
        year_data = []
        grouping = "day"

        try:
            qsales = all_concepts_seller_yearly.filter(
                invoice__status__code="revised",
                invoice__invoice_category__code="sales",
            )
            qexpenses = all_concepts_seller_yearly.filter(
                invoice__status__code="revised",
                invoice__invoice_category__code="expenses",
            )

            ingresospormes = (
                qsales.exclude(invoice__transaction_type__code='import-dua').annotate(
                    month=TruncMonth('invoice__accounting_date'))
                .values('month')
                .annotate(
                    total=Sum(
                        Case(
                            When(invoice__is_txt_amz=True, then=F('amount_euros')),
                            default=F('amount_euros') * F('quantity'),
                            output_field=DecimalField(),
                        )
                    )
                )
                .order_by('month')
            )

            for i, ingreso in enumerate(ingresospormes):
                date = ingreso['month'].strftime('%Y-%m') if ingreso['month'] else None

                if date is None:
                    prev_ingreso = ingresospormes[i - 1] if i > 0 else None
                    next_ingreso = ingresospormes[i + 1] if i < len(ingresospormes) - 1 else None

                    if prev_ingreso and prev_ingreso['month']:
                        date = prev_ingreso['month'].strftime('%Y-%m')
                    elif next_ingreso and next_ingreso['month']:
                        date = next_ingreso['month'].strftime('%Y-%m')

                ingreso['total'] = float(ingreso['total'])
                year_data.append({"date": date, "income": round(ingreso['total'], 2), "expense": 0, "benefit": 0})

            year_data = sorted(year_data, key=lambda x: x['date'])

            for values in year_data:
                values['income'] = decimal.Decimal(str(values['income']))
                values['expense'] = decimal.Decimal(str(values['expense']))
                benefit = values['income'] - values['expense']
                values['benefit'] = round(benefit, 2)

            gastospormes = (
                qexpenses.exclude(invoice__transaction_type__code='import-dua').annotate(
                    month=TruncMonth('invoice__accounting_date'))
                .values('month')
                .annotate(
                    total=Sum(
                        Case(
                            When(invoice__is_txt_amz=True, then=0),
                            default=F('amount_euros') * F('quantity'),
                            output_field=DecimalField(),
                        )
                    )
                )
                .order_by('month')
            )

            for i, gasto in enumerate(gastospormes):
                date = gasto['month'].strftime('%Y-%m') if gasto['month'] else None

                if date is None:
                    prev_gasto = gastospormes[i - 1] if i > 0 else None
                    next_gasto = gastospormes[i + 1] if i < len(gastospormes) - 1 else None

                    if prev_gasto and prev_gasto['month']:
                        date = prev_gasto['month'].strftime('%Y-%m')
                    elif next_gasto and next_gasto['month']:
                        date = next_gasto['month'].strftime('%Y-%m')

                existing_entry = next((d for d in year_data if d["date"] == date), None)

                if existing_entry:
                    existing_entry["expense"] = gasto['total']
                else:
                    year_data.append({"date": date, "income": 0, "expense": round(gasto['total'], 2), "benefit": 0})

            year_data = sorted(year_data, key=lambda x: x['date'])

            for values in year_data:
                values['income'] = decimal.Decimal(str(values['income']))
                values['expense'] = decimal.Decimal(str(values['expense']))
                benefit = values['income'] - values['expense']
                values['benefit'] = round(benefit, 2)

            totals = {}

            for result in year_data:
                date = result['date']
                totals.setdefault(date, {"income": 0, "expense": 0, "benefit": 0})
                totals[date]['income'] += round(result['income'], 2)
                totals[date]['expense'] += round(result['expense'], 2)

            for date, values in totals.items():
                benefit = values['income'] - values['expense']
                values['benefit'] = round(benefit, 2)

            year_data = [{"date": date, "income": round(values['income'], 2), "expense": round(values['expense'], 2),
                          "benefit": round(values['benefit'], 2)} for date, values in totals.items()]

            # Convertir los valores de tipo Decimal a float
            for values in year_data:
                values['income'] = float(values['income'])
                values['expense'] = float(values['expense'])
                values['benefit'] = float(values['benefit'])
            year_data = json.dumps(year_data, ensure_ascii=False)

        except Exception as e:
            print("Error year_data: " + str(e) + '-Error en la línea:' + str(traceback.format_exc().splitlines()[-2]))

        try:
            qs = all_concepts_seller.filter(
                invoice__status__code="revised",
                invoice__invoice_category__code="sales",
            )

            amz = qs.exclude(invoice__transaction_type__code='import-dua').filter(invoice__is_txt_amz=True).aggregate(
                total=Sum(F('amount_euros')))['total']
            common = \
                qs.exclude(invoice__transaction_type__code='import-dua').exclude(invoice__is_txt_amz=True).aggregate(
                    total=Sum(F('amount_euros') * F('quantity')))['total']
            if amz and amz is not None:
                ingresostotalbase += amz
            if common and common is not None:
                ingresostotalbase += common

            amz = qs.filter(invoice__is_txt_amz=True).aggregate(total=Sum(F('amount_euros') * F('vat') / 100))['total']
            common = qs.exclude(invoice__is_txt_amz=True).aggregate(
                total=Sum(F('amount_euros') * F('quantity') * F('vat') / 100))['total']
            if amz and amz is not None:
                ingresostotaliva += amz
            if common and common is not None:
                ingresostotaliva += common

            amz = qs.filter(invoice__is_txt_amz=True).aggregate(total=Sum(F('amount_euros') * F('irpf') / 100))['total']
            common = qs.exclude(invoice__is_txt_amz=True).aggregate(
                total=Sum(F('amount_euros') * F('quantity') * F('irpf') / 100))['total']
            if amz and amz is not None:
                ingresostotalirpf += amz
            if common and common is not None:
                ingresostotalirpf += common
            if ingresostotalirpf is None:
                ingresostotalirpf = 0

            # Antes del código existente
            grouping = 'day'  # Opción por defecto para agrupar por día

            # En el caso de que se seleccione un mes, se mostrarán los ingresos por día
            if month != "all" and month != "tri1" and month != "tri2" and month != "tri3" and month != "tri4":
                grouping = 'day'

                ingresospordia = qs.exclude(invoice__transaction_type__code='import-dua').values(
                    'invoice__accounting_date').annotate(total=Sum(F('amount_euros'))).order_by(
                    'invoice__accounting_date')
                for ingreso in ingresospordia:
                    date = ingreso['invoice__accounting_date']
                    if date:
                        date = date.isoformat()

                    results_data.append({"date": date, "income": float(ingreso['total']), "expense": 0, "benefit": 0})

                def get_date(item):
                    date = item['date']
                    if date is None:
                        return ''
                    return date

                results_data = sorted(results_data, key=lambda x: get_date(x))

            # Ingresos por semana
            elif month == "tri1" or month == "tri2" or month == "tri3" or month == "tri4":
                grouping = 'week'

                ingresosporsemana = qs.exclude(invoice__transaction_type__code='import-dua').annotate(
                    week=TruncWeek('invoice__accounting_date')).values('week').annotate(
                    total=Sum('amount_euros')).order_by('week')

                for ingreso in ingresosporsemana:
                    week_start = ingreso['week']
                    week_end = ingreso['week'] + timedelta(days=6)
                    if week_start:
                        week_start = week_start.isoformat()
                    if week_end:
                        week_end = week_end.isoformat()

                    results_data.append(
                        {"date": week_start, "income": float(ingreso['total']), "expense": 0, "benefit": 0})

                def get_date(item):
                    date = item['date']
                    if date is None:
                        return ''
                    return date

                results_data = sorted(results_data, key=lambda x: get_date(x))

            # Ingresos por mes
            else:
                grouping = 'month'

                ingresospormes = qs.exclude(invoice__transaction_type__code='import-dua').values(
                    'invoice__accounting_date').annotate(total=Sum(F('amount_euros'))).order_by(
                    'invoice__accounting_date')
                for ingreso in ingresospormes:
                    date = ingreso['invoice__accounting_date'].strftime('%Y-%m')
                    results_data.append({"date": date, "income": ingreso['total'], "expense": 0, "benefit": 0})

            marketplaces = qs.values_list('invoice__marketplace__description', flat=True).distinct()
            total_income = \
                qs.exclude(invoice__transaction_type__code='import-dua').aggregate(total=Sum('amount_euros'))['total']
            for marketplace in marketplaces:
                if marketplace == None:
                    continue

                income = qs.exclude(invoice__transaction_type__code='import-dua').filter(
                    invoice__marketplace__description=marketplace).aggregate(total=Sum('amount_euros'))['total']
                percentage = round(income / total_income * 100, 2)
                if len(marketplace) > 15:
                    marketplace = marketplace[:15] + "..."
                marketplace_data.append({"name": marketplace, "income": round(income, 2), "percentage": percentage})
            # Ordenar el array marketplace_data de mayor a menor por el campo income
            marketplace_data = sorted(marketplace_data, key=lambda x: x["income"], reverse=True)
            # Agrupar los marketplaces que estén en la posición 15 o superior en un marketplace llamado "otros"
            if len(marketplace_data) > 14:
                other_income = sum([m["income"] for m in marketplace_data[14:]])
                other_percentage = round(sum([m["percentage"] for m in marketplace_data[14:]]), 2)
                marketplace_data = marketplace_data[:14] + [
                    {"name": "otros", "income": round(other_income, 2), "percentage": other_percentage}]

            qsm = qs.filter(invoice__marketplace__isnull=True)
            if qsm.count() > 0:
                income = qsm.exclude(invoice__transaction_type__code='import-dua').aggregate(total=Sum('amount_euros'))[
                    'total']
                percentage = round(income / total_income * 100, 2)
                marketplace_data.append(
                    {"name": "Ventas sin marketplace", "income": round(income, 2), "percentage": percentage})

            marketplace_data = json.dumps(marketplace_data, ensure_ascii=False)

        except Exception as e:
            print("Error sales: " + str(e) + '-Error en la línea:' + str(traceback.format_exc().splitlines()[-2]))
            ingresostotalbase = 0
            ingresostotaliva = 0
            ingresostotalirpf = 0

        gastostotalbase = 0
        gastostotaliva = 0
        gastostotalirpf = 0

        try:
            qs = all_concepts_seller.filter(
                invoice__status__code="revised",
                invoice__invoice_category__code="expenses",
            )
            amz = qs.exclude(invoice__transaction_type__code='import-dua').filter(invoice__is_txt_amz=True).aggregate(
                total=Sum(F('amount_euros')))['total']
            common = \
                qs.exclude(invoice__transaction_type__code='import-dua').exclude(invoice__is_txt_amz=True).aggregate(
                    total=Sum(F('amount_euros') * F('quantity')))['total']
            if amz and amz is not None:
                gastostotalbase += amz
            if common and common is not None:
                gastostotalbase += common

            amz = qs.filter(invoice__is_txt_amz=True).aggregate(total=Sum(F('amount_euros') * F('vat') / 100))['total']
            common = qs.exclude(invoice__is_txt_amz=True).aggregate(
                total=Sum(F('amount_euros') * F('quantity') * F('vat') / 100))['total']
            if amz and amz is not None:
                gastostotaliva += amz
            if common and common is not None:
                gastostotaliva += common

            amz = qs.filter(invoice__is_txt_amz=True).aggregate(total=Sum(F('amount_euros') * F('irpf') / 100))['total']
            common = qs.exclude(invoice__is_txt_amz=True).aggregate(
                total=Sum(F('amount_euros') * F('quantity') * F('irpf') / 100))['total']
            if amz and amz is not None:
                gastostotalirpf += amz
            if common and common is not None:
                gastostotalirpf += common
            if gastostotalirpf is None:
                gastostotalirpf = 0

            gastospordia = qs.exclude(invoice__transaction_type__code='import-dua').values(
                'invoice__accounting_date').annotate(total=Sum(F('amount_euros'))).order_by('invoice__accounting_date')
            for gasto in gastospordia:
                date = gasto['invoice__accounting_date']
                if date:
                    date = date.isoformat()

                results_data.append({"date": date, "income": 0, "expense": float(gasto['total']), "benefit": 0})

            def get_date(item):
                date = item['date']
                if date is None:
                    return ''
                return date

            results_data = sorted(results_data, key=lambda x: get_date(x))
            totals = {}
            for result in results_data:
                date = result['date']
                totals.setdefault(date, {'income': 0, 'expense': 0})
                totals[date]['income'] += result['income']
                totals[date]['expense'] += result['expense']
            for date, values in totals.items():
                benefit = values['income'] - values['expense']
                values['benefit'] = benefit
            results_data = [
                {'date': date, 'income': values['income'], 'expense': values['expense'], 'benefit': values['benefit']}
                for date, values in totals.items()]

            providers = qs.values_list('invoice__provider__name', flat=True).distinct()
            total_expense = \
                qs.exclude(invoice__transaction_type__code='import-dua').aggregate(total=Sum('amount_euros'))['total']
            if total_expense == None:
                total_expense = 0
            for provider in providers:
                if provider == None:
                    continue

                expenses = qs.exclude(invoice__transaction_type__code='import-dua').filter(
                    invoice__provider__name=provider).aggregate(total=Sum('amount_euros'))['total']
                if expenses == None:
                    expenses = 0
                percentage = round(float(expenses) / total_expense * 100, 2) if total_expense != 0 else 0
                if len(provider) > 15:
                    provider = provider[:15] + "..."
                providers_data.append({"name": provider, "expenses": round(expenses, 2), "percentage": percentage})
            # Ordenar el array providers_data de mayor a menor por el campo expenses
            providers_data = sorted(providers_data, key=lambda x: x["expenses"], reverse=True)

            # Agrupar los proveedores que estén en la posición 15 o superior en un proveedor llamado "otros"
            if len(providers_data) > 14:
                other_expense = sum([p["expenses"] for p in providers_data[14:]])
                other_percentage = round(sum([p["percentage"] for p in providers_data[14:]]), 2)
                providers_data = providers_data[:14] + [
                    {"name": "otros", "expenses": round(other_expenses, 2), "percentage": other_percentage}]
            # providers_data = json.dumps(providers_data, ensure_ascii=False)

        except Exception as e:
            print("Error expenses: " + str(e) + '-Error en la línea:', traceback.format_exc().splitlines()[-2])
            gastostotalbase = 0
            gastostotaliva = 0
            gastostotalirpf = 0

        providers_data = json.dumps(providers_data, ensure_ascii=False)
        totalfacturas = all_concepts_seller.filter(invoice__status__code="revised").count()

        if totalfacturas == 0:
            stats = {}
            stats['ingresosbase'] = 0
            stats['ingresosiva'] = 0
            stats['ingresosirpf'] = 0
            stats['gastosbase'] = 0
            stats['gastosiva'] = 0
            stats['gastosirpf'] = 0
            stats['beneficiosbase'] = 0
            stats['beneficiosbaseINT'] = 0
            stats['beneficiosiva'] = 0
            stats['beneficiosirpf'] = 0
            stats['ingresosgraf'] = 0
            stats['gastosgraf'] = 0
            stats['beneficiosgraf'] = 0
            stats['marketplace_data'] = []
            stats['providers_data'] = []
            stats['results_data'] = []
            stats['year_data'] = []
            stats['grouping'] = ""
            context['stat'] = stats
        else:
            stats = {}
            ingresosbase = round(ingresostotalbase, 2) if ingresostotalbase else 0
            stats['ingresosbase'] = "{:,}".format(ingresosbase).replace(',', '~').replace('.', ',').replace('~', '.')
            stats['ingresosgraf'] = ingresosbase
            ingresosiva = round(ingresostotaliva, 2) if ingresostotaliva else 0
            stats['ingresosiva'] = "{:,}".format(ingresosiva).replace(',', '~').replace('.', ',').replace('~', '.')
            ingresosirpf = round(ingresostotalirpf, 2) if ingresostotalirpf else 0
            stats['ingresosirpf'] = "{:,}".format(ingresosirpf).replace(',', '~').replace('.', ',').replace('~', '.')

            gastosbase = round(gastostotalbase, 2) if gastostotalbase else 0
            stats['gastosbase'] = "{:,}".format(gastosbase).replace(',', '~').replace('.', ',').replace('~', '.')
            stats['gastosgraf'] = gastosbase * -1
            gastosiva = round(gastostotaliva, 2) if gastostotaliva else 0
            stats['gastosiva'] = "{:,}".format(gastosiva).replace(',', '~').replace('.', ',').replace('~', '.')
            gastosirpf = round(gastostotalirpf, 2) if gastostotalirpf else 0
            stats['gastosirpf'] = "{:,}".format(gastosirpf).replace(',', '~').replace('.', ',').replace('~', '.')

            beneficiosbase = round(ingresosbase - gastosbase, 2)
            stats['beneficiosbaseINT'] = beneficiosbase
            stats['beneficiosbase'] = "{:,}".format(beneficiosbase).replace(',', '~').replace('.', ',').replace('~',
                                                                                                                '.')
            stats['beneficiosgraf'] = beneficiosbase
            beneficiosiva = round(ingresosiva - gastosiva, 2)
            stats['beneficiosiva'] = "{:,}".format(beneficiosiva).replace(',', '~').replace('.', ',').replace('~', '.')
            beneficiosirpf = round(ingresosirpf - gastosirpf, 2)
            stats['beneficiosirpf'] = "{:,}".format(beneficiosirpf).replace(',', '~').replace('.', ',').replace('~',
                                                                                                                '.')
            stats['marketplace_data'] = marketplace_data
            stats['providers_data'] = providers_data
            stats['results_data'] = results_data
            stats['year_data'] = year_data
            stats['grouping'] = grouping

            context['stat'] = stats

        # Añadir el cálculo de estadísticas de facturas
        # Datos de facturas
        invoice_data = {
            'num_sales_total': 0,
            'num_sales_pending': 0,
            'num_sales_revised': 0,
            'num_sales_discarded': 0,
            'percentage_sales_pending': 0,
            'percentage_sales_revised': 0,
            'percentage_sales_discarded': 0,

            'num_expenses_total': 0,
            'num_expenses_pending': 0,
            'num_expenses_revised': 0,
            'num_expenses_discarded': 0,
            'percentage_expenses_pending': 0,
            'percentage_expenses_revised': 0,
            'percentage_expenses_discarded': 0,

            'num_grand_total': 0,
            'num_grand_total_pending': 0,
            'num_grand_total_revised': 0,
            'num_grand_total_discarded': 0,
            'percentage_grand_total_pending': 0,
            'percentage_grand_total_revised': 0,
            'percentage_grand_total_discarded': 0,
        }

        if seller:
            # Consulta base: facturas del seller
            all_invoices_base_query = Invoice.objects.filter(seller=seller)
            pending_statuses = ['pending', 'revision-pending']
            revised_status = 'revised'
            discard_status = 'discard'

            # Facturas de Venta
            sales_invoices_query = all_invoices_base_query.filter(invoice_category__code='sales')
            invoice_data['num_sales_total'] = sales_invoices_query.count()
            if invoice_data['num_sales_total'] > 0:
                invoice_data['num_sales_pending'] = sales_invoices_query.filter(status__code__in=pending_statuses).count()
                invoice_data['num_sales_revised'] = sales_invoices_query.filter(status__code=revised_status).count()
                invoice_data['num_sales_discarded'] = sales_invoices_query.filter(status__code=discard_status).count()

                invoice_data['percentage_sales_pending'] = int((invoice_data['num_sales_pending'] / invoice_data['num_sales_total']) * 100)
                invoice_data['percentage_sales_revised'] = int((invoice_data['num_sales_revised'] / invoice_data['num_sales_total']) * 100)
                invoice_data['percentage_sales_discarded'] = int((invoice_data['num_sales_discarded'] / invoice_data['num_sales_total']) * 100)

            # Facturas de Gasto
            expenses_invoices_query = all_invoices_base_query.filter(invoice_category__code='expenses')
            invoice_data['num_expenses_total'] = expenses_invoices_query.count()
            if invoice_data['num_expenses_total'] > 0:
                invoice_data['num_expenses_pending'] = expenses_invoices_query.filter(status__code__in=pending_statuses).count()
                invoice_data['num_expenses_revised'] = expenses_invoices_query.filter(status__code=revised_status).count()
                invoice_data['num_expenses_discarded'] = expenses_invoices_query.filter(status__code=discard_status).count()

                invoice_data['percentage_expenses_pending'] = int((invoice_data['num_expenses_pending'] / invoice_data['num_expenses_total']) * 100)
                invoice_data['percentage_expenses_revised'] = int((invoice_data['num_expenses_revised'] / invoice_data['num_expenses_total']) * 100)
                invoice_data['percentage_expenses_discarded'] = int((invoice_data['num_expenses_discarded'] / invoice_data['num_expenses_total']) * 100)

            # Totales Generales (Ventas + Gastos)
            relevant_invoices_query = all_invoices_base_query.filter(invoice_category__code__in=['sales', 'expenses'])
            invoice_data['num_grand_total'] = relevant_invoices_query.count()
            if invoice_data['num_grand_total'] > 0:
                invoice_data['num_grand_total_pending'] = relevant_invoices_query.filter(status__code__in=pending_statuses).count()
                invoice_data['num_grand_total_revised'] = relevant_invoices_query.filter(status__code=revised_status).count()
                invoice_data['num_grand_total_discarded'] = relevant_invoices_query.filter(status__code=discard_status).count()

                invoice_data['percentage_grand_total_pending'] = int((invoice_data['num_grand_total_pending'] / invoice_data['num_grand_total']) * 100)
                invoice_data['percentage_grand_total_revised'] = int((invoice_data['num_grand_total_revised'] / invoice_data['num_grand_total']) * 100)
                invoice_data['percentage_grand_total_discarded'] = int((invoice_data['num_grand_total_discarded'] / invoice_data['num_grand_total']) * 100)

        context['invoice_data'] = invoice_data

        return context

    def handle_no_permission(self):
        return HttpResponseRedirect(reverse("home"))

class UserDashFilter(LoginRequiredMixin, IsSellerRolePermission, TemplateView):
    model = User
    template_name = "home/index.html"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        user = self.request.user
        seller = user.seller
        country = self.kwargs.get('country', 'all').strip()
        iaeSelected = self.kwargs.get('iae', 'all').strip()
        year = self.kwargs.get('year', '').strip()
        month = self.kwargs.get('month', '').strip()

        # Obtener conteo de tareas pendientes no vistas
        pending_tasks_query = TaskPending.objects.filter(user=user, completed=False)
        context['pending_tasks_count'] = pending_tasks_query.count()
        context['alert_pending_notification'] = pending_tasks_query.exists()
        context['affiliate_program'] = seller.affiliate_program

        session_message = ['submit5472', 'submitBE15', 'submitSellerRapForm', 'submit184']
        for form_key in session_message:
            context[form_key] = self.request.session.pop(form_key, None)

        seller_vats = SellerVat.objects.filter(seller=seller)
        seller_vat_activity = SellerVatActivity.objects.filter(sellervat__in=seller_vats).order_by('sellervat_activity_iae__description').distinct('sellervat_activity_iae__description')

        context['iae'] = [self.process_iae_context(activity.sellervat_activity_iae) for activity in seller_vat_activity] if seller_vat_activity.count() > 1 else []
        context['seller_vat'] = seller_vats
        context.update(self.get_seller_related_counts(seller, seller_vats))
        context['formUser'] = UserChangeForm(instance=user)
        context['formSeller'] = sellerChangeForm(instance=seller)
        context['formPassword'] = PasswordChangeForm(user=user)

        invoices, invoices_year = self.prefetch_invoices(seller, year, month, country, iaeSelected)

        context['filter_year'] = year
        context['filter_month'] = month
        context['filter_country'] = country
        context['filter_iae'] = iaeSelected

        # Generar título dinámico para la sección de estado de facturas
        invoice_stats_period_label = self.generate_invoice_stats_title(year, month)
        context['invoice_stats_period_label'] = invoice_stats_period_label

        # Añadir el cálculo de estadísticas de facturas
        # Datos de facturas
        invoice_data = {
            'num_sales_total': 0,
            'num_sales_pending': 0,
            'num_sales_revised': 0,
            'num_sales_discarded': 0,
            'percentage_sales_pending': 0,
            'percentage_sales_revised': 0,
            'percentage_sales_discarded': 0,

            'num_expenses_total': 0,
            'num_expenses_pending': 0,
            'num_expenses_revised': 0,
            'num_expenses_discarded': 0,
            'percentage_expenses_pending': 0,
            'percentage_expenses_revised': 0,
            'percentage_expenses_discarded': 0,

            'num_grand_total': 0,
            'num_grand_total_pending': 0,
            'num_grand_total_revised': 0,
            'num_grand_total_discarded': 0,
            'percentage_grand_total_pending': 0,
            'percentage_grand_total_revised': 0,
            'percentage_grand_total_discarded': 0,
        }

        if seller:
            # Consulta base: facturas del seller
            # Filtrar las facturas según los criterios de tiempo actuales
            all_invoices_base_query = Invoice.objects.filter(seller=seller)

            # Aplicar los mismos filtros de tiempo que se aplican en prefetch_invoices
            if year != "all":
                all_invoices_base_query = all_invoices_base_query.filter(accounting_date__year=year)
            if month != "all":
                all_invoices_base_query = self.filter_invoices_by_month(all_invoices_base_query, month)
            if country != "all":
                all_invoices_base_query = all_invoices_base_query.filter(tax_country__iso_code=country)
            if iaeSelected != "all":
                all_invoices_base_query = all_invoices_base_query.filter(iae__code__endswith=iaeSelected)

            pending_statuses = ['pending', 'revision-pending']
            revised_status = 'revised'
            discard_status = 'discard'

            # Facturas de Venta
            sales_invoices_query = all_invoices_base_query.filter(invoice_category__code='sales')
            invoice_data['num_sales_total'] = sales_invoices_query.count()
            if invoice_data['num_sales_total'] > 0:
                invoice_data['num_sales_pending'] = sales_invoices_query.filter(status__code__in=pending_statuses).count()
                invoice_data['num_sales_revised'] = sales_invoices_query.filter(status__code=revised_status).count()
                invoice_data['num_sales_discarded'] = sales_invoices_query.filter(status__code=discard_status).count()

                invoice_data['percentage_sales_pending'] = int((invoice_data['num_sales_pending'] / invoice_data['num_sales_total']) * 100)
                invoice_data['percentage_sales_revised'] = int((invoice_data['num_sales_revised'] / invoice_data['num_sales_total']) * 100)
                invoice_data['percentage_sales_discarded'] = int((invoice_data['num_sales_discarded'] / invoice_data['num_sales_total']) * 100)

            # Facturas de Gasto
            expenses_invoices_query = all_invoices_base_query.filter(invoice_category__code='expenses')
            invoice_data['num_expenses_total'] = expenses_invoices_query.count()
            if invoice_data['num_expenses_total'] > 0:
                invoice_data['num_expenses_pending'] = expenses_invoices_query.filter(status__code__in=pending_statuses).count()
                invoice_data['num_expenses_revised'] = expenses_invoices_query.filter(status__code=revised_status).count()
                invoice_data['num_expenses_discarded'] = expenses_invoices_query.filter(status__code=discard_status).count()

                invoice_data['percentage_expenses_pending'] = int((invoice_data['num_expenses_pending'] / invoice_data['num_expenses_total']) * 100)
                invoice_data['percentage_expenses_revised'] = int((invoice_data['num_expenses_revised'] / invoice_data['num_expenses_total']) * 100)
                invoice_data['percentage_expenses_discarded'] = int((invoice_data['num_expenses_discarded'] / invoice_data['num_expenses_total']) * 100)

            # Totales Generales (Ventas + Gastos)
            relevant_invoices_query = all_invoices_base_query.filter(invoice_category__code__in=['sales', 'expenses'])
            invoice_data['num_grand_total'] = relevant_invoices_query.count()
            if invoice_data['num_grand_total'] > 0:
                invoice_data['num_grand_total_pending'] = relevant_invoices_query.filter(status__code__in=pending_statuses).count()
                invoice_data['num_grand_total_revised'] = relevant_invoices_query.filter(status__code=revised_status).count()
                invoice_data['num_grand_total_discarded'] = relevant_invoices_query.filter(status__code=discard_status).count()

                invoice_data['percentage_grand_total_pending'] = int((invoice_data['num_grand_total_pending'] / invoice_data['num_grand_total']) * 100)
                invoice_data['percentage_grand_total_revised'] = int((invoice_data['num_grand_total_revised'] / invoice_data['num_grand_total']) * 100)
                invoice_data['percentage_grand_total_discarded'] = int((invoice_data['num_grand_total_discarded'] / invoice_data['num_grand_total']) * 100)

        context['invoice_data'] = invoice_data

        stats = {}
        if not invoices.exists():
            stats['ingresosbase'] = 0
            stats['ingresosiva'] = 0
            stats['ingresosirpf'] = 0
            stats['gastosbase'] = 0
            stats['gastosiva'] = 0
            stats['gastosirpf'] = 0
            stats['beneficiosbase'] = 0
            stats['beneficiosiva'] = 0
            stats['beneficiosirpf'] = 0
            stats['ingresosgraf'] = 0
            stats['gastosgraf'] = 0
            stats['beneficiosgraf'] = 0
            stats['marketplace_data'] = []
            stats['providers_data'] = []
            stats['results_data'] = []
            stats['year_data'] = []
            stats['grouping'] = ""
            context['stat'] = stats
        else:
            all_concepts_seller = Concept.objects.filter(invoice__in=invoices).exclude(is_supplied=True).only(
                'invoice__accounting_date', 'amount_euros', 'amount_original', 'quantity', 'invoice__status__code',
                'invoice__invoice_category__code', 'invoice__is_txt_amz', 'invoice__transaction_type__code',
                'invoice__marketplace__description', 'invoice__provider__name', 'vat', 'irpf'
            )
            all_concepts_seller_yearly = Concept.objects.filter(invoice__in=invoices_year).exclude(is_supplied=True).only(
                'invoice__accounting_date', 'amount_euros', 'amount_original', 'quantity', 'invoice__status__code',
                'invoice__invoice_category__code', 'invoice__is_txt_amz', 'invoice__transaction_type__code',
            )

            concepts_df = pd.DataFrame.from_records(
                all_concepts_seller.values(
                    'invoice__accounting_date', 'amount_euros', 'amount_original', 'quantity', 'invoice__status__code',
                    'invoice__invoice_category__code', 'invoice__is_txt_amz', 'invoice__transaction_type__code',
                    'invoice__marketplace__description', 'invoice__provider__name', 'vat', 'irpf'
                )
            )

            yearly_concepts_df = pd.DataFrame.from_records(
                all_concepts_seller_yearly.values(
                    'invoice__accounting_date', 'amount_euros', 'amount_original', 'quantity', 'invoice__status__code',
                    'invoice__invoice_category__code', 'invoice__is_txt_amz', 'invoice__transaction_type__code',
                )
            )

            year_data = self.process_yearly_data(yearly_concepts_df)
            marketplace_data = self.process_marketplace_data(concepts_df, month)
            providers_data = self.process_providers_data(concepts_df)
            merged_results_data = marketplace_data.get('results_data', []) + providers_data.get('results_data', [])
            merged_results_datasorted = sorted(merged_results_data, key=lambda x: datetime.strptime(x['date'], '%Y-%m-%d') if len(x['date']) > 7 else datetime.strptime(x['date'], '%Y-%m'), reverse=False)

            beneficiosbase = marketplace_data.get('ingresosgraf', 0) + providers_data.get('gastosgraf', 0)
            beneficiosiva = marketplace_data.get('ingresosivafloat', 0) + providers_data.get('gastosivafloat', 0)
            beneficiosirpf = marketplace_data.get('ingresosirpffloat', 0) + providers_data.get('gastosirpffloat', 0)

            beneficiosbase = round(beneficiosbase, 2)
            beneficiosiva = round(beneficiosiva, 2)
            beneficiosirpf = round(beneficiosirpf, 2)

            stats = {**marketplace_data, **providers_data}
            stats['results_data'] = merged_results_datasorted
            stats['beneficiosbase'] = format_decimal(beneficiosbase, locale='es_ES')
            stats['beneficiosgraf'] = beneficiosbase
            stats['beneficiosiva'] = format_decimal(beneficiosiva, locale='es_ES')
            stats['beneficiosirpf'] = format_decimal(beneficiosirpf, locale='es_ES')


            stats['year_data'] = json.dumps(year_data, ensure_ascii=False)
            context['stat'] = stats

        return context

    def process_iae_context(self, iae):
        """Procesar el IAE para mostrarlo en la vista"""
        modified_iae = copy.deepcopy(iae)
        if '-' in modified_iae.code:
            modified_iae.code = modified_iae.code.split('-')[-1]
        return modified_iae

    def get_seller_related_counts(self, seller, seller_vats):
        """Obtener los conteos de los objetos relacionados con el vendedor"""
        cache_key = f'seller_objects_count_{seller.id}'
        counts = cache.get(cache_key)
        if counts is None:
            counts = {
                'seller_vat_count': seller_vats.count(),
                'customers_count': Customer.objects.filter(seller=seller).count(),
                'providers_count': Provider.objects.filter(seller=seller).count(),
                'partners_count': Partner.objects.filter(seller=seller).count(),
                'workers_count': Worker.objects.filter(seller=seller).count(),
                'invoices_count': Invoice.objects.filter(seller=seller).count(),
            }
            cache.set(cache_key, counts, timeout=60 * 5)

        return counts

    def prefetch_invoices(self, seller, year, month, country, iaeSelected):
        """Obtener las facturas del vendedor con prefetch"""
        invoices = Invoice.objects.filter(
            seller=seller,
            status__code='revised',
            invoice_category__code__in=['sales', 'expenses']
        ).exclude(
            is_generated_amz=True
        ).exclude(
            transaction_type__code__icontains='-transfer'
        ).exclude(
            is_postponed_import_vat=True
        )
        invoices_year = invoices

        if year != "all":
            invoices = invoices.filter(accounting_date__year=year)
            invoices_year = invoices
        if month != "all":
            invoices = self.filter_invoices_by_month(invoices, month)
        if country != "all":
            invoices = invoices.filter(tax_country__iso_code=country)
        if iaeSelected != "all":
            invoices = invoices.filter(iae__code__endswith=iaeSelected)

        # invoices = invoices.prefetch_related('concepts', 'transaction_type', 'invoice_category', 'marketplace', 'provider', 'tax_country')
        return invoices, invoices_year

    def filter_invoices_by_month(self, invoices, month):
        """Filtrar las facturas por mes"""
        if month == "tri1":
            invoices = invoices.filter(accounting_date__month__in=[1, 2, 3])
        elif month == "tri2":
            invoices = invoices.filter(accounting_date__month__in=[4, 5, 6])
        elif month == "tri3":
            invoices = invoices.filter(accounting_date__month__in=[7, 8, 9])
        elif month == "tri4":
            invoices = invoices.filter(accounting_date__month__in=[10, 11, 12])
        else:
            invoices = invoices.filter(accounting_date__month=month)

        return invoices

    def process_yearly_data(self, yearly_concepts_df: pd.DataFrame):
        """Procesar los datos del año usando un DataFrame"""
        # filtrar solo las facturas revisadas y agregar la columna de fecha
        df = yearly_concepts_df.copy()
        df['date'] = pd.to_datetime(df['invoice__accounting_date'], errors='coerce')
        df.dropna(subset=['date'], inplace=True)

        df['date'] = df['date'].dt.to_period('M')

        # separar las ventas de los gastos
        sales_df = df[df['invoice__invoice_category__code'] == 'sales']
        expenses_df = df[df['invoice__invoice_category__code'] == 'expenses']

        # Eliminar las facturas de importación y calcular los ingresos agrupados por mes
        sales_df = sales_df[~sales_df['invoice__transaction_type__code'].str.contains('import-dua', na=False)]
        sales_agg = sales_df.assign(
            income=np.where(sales_df['invoice__is_txt_amz'],
                            sales_df['amount_euros'],
                            sales_df['amount_euros'] * sales_df['quantity'])
        ).groupby('date')['income'].sum().round(2).reset_index()

        # Eliminar las facturas de importación y calcular los gastos agrupados por mes
        expenses_df = expenses_df[~expenses_df['invoice__transaction_type__code'].str.contains('import-dua', na=False)]
        expenses_agg = expenses_df.assign(
            expense=np.where(expenses_df['invoice__is_txt_amz'],
                            0,
                            expenses_df['amount_euros'] * expenses_df['quantity'])
        ).groupby('date')['expense'].sum().round(2).reset_index()

        year_data = pd.merge(sales_agg, expenses_agg, on='date', how='outer').fillna(0)
        year_data['benefit'] = (year_data['income'] - year_data['expense']).round(2)
        year_data['date'] = year_data['date'].astype(str)

        del sales_df, expenses_df, sales_agg, expenses_agg

        return year_data.to_dict(orient='records')

    def process_marketplace_data(self, concepts_df: pd.DataFrame, month: str):
        """Procesar los datos de los marketplaces usando un DataFrame"""
        income_stats = {}

        copied_df = concepts_df[concepts_df['invoice__invoice_category__code'] == 'sales'].copy()

        copied_df['invoice__accounting_date'] = pd.to_datetime(copied_df['invoice__accounting_date'], errors='coerce')
        copied_df.dropna(subset=['invoice__accounting_date'], inplace=True)

        # remover las facturas de importación
        sales_df = copied_df[~copied_df['invoice__transaction_type__code'].str.contains('import-dua', na=False)]

        # calcular los ingresos agrupados por marketplace
        sales_df['income'] = np.where(
            sales_df['invoice__is_txt_amz'],
            sales_df['amount_euros'],
            sales_df['amount_euros'] * sales_df['quantity']
        )
        ingresostotalbase = sales_df['income'].sum().round(2)

        # calcular el IVA agrupado por marketplace
        sales_df['vat'] = np.where(
            sales_df['invoice__is_txt_amz'],
            sales_df['amount_euros'] * sales_df['vat'] / 100,
            sales_df['amount_euros'] * sales_df['quantity'] * sales_df['vat'] / 100
        )
        ingresostotaliva = sales_df['vat'].sum().round(2)

        # calcular el IRPF agrupado por marketplace
        sales_df['irpf'] = np.where(
            sales_df['invoice__is_txt_amz'],
            sales_df['amount_euros'] * sales_df['irpf'] / 100,
            sales_df['amount_euros'] * sales_df['quantity'] * sales_df['irpf'] / 100
        )
        ingresostotalirpf = sales_df['irpf'].sum().round(2)

        # grouping para mostrar los datos en el gráfico
        grouping = 'day'  # Opción por defecto para agrupar por día
        if month != "all" and month not in ["tri1", "tri2", "tri3", "tri4"]:
            # Agrupar por día
            results_data = (sales_df.groupby('invoice__accounting_date')['income'].sum().reset_index()).round(2)
            results_data['date'] = results_data['invoice__accounting_date'].dt.date.astype(str)
            results_data['expense'] = 0
            results_data['benefit'] = results_data['income']

        elif month in ["tri1", "tri2", "tri3", "tri4"]:
            # Agrupar por semana
            grouping = 'week'
            results_data = sales_df.groupby(pd.Grouper(key='invoice__accounting_date', freq='W'))['income'].sum().reset_index()
            results_data['date'] = results_data['invoice__accounting_date'].dt.date.astype(str)
            results_data['expense'] = 0
            results_data['benefit'] = results_data['income']

        else:
            # Agrupar por mes
            grouping = 'month'
            results_data = (sales_df.groupby(pd.Grouper(key='invoice__accounting_date', freq='M'))['income'].sum().reset_index()).round(2)
            results_data['date'] = results_data['invoice__accounting_date'].dt.to_period('M').astype(str)
            results_data['expense'] = 0
            results_data['benefit'] = results_data['income']

        # Calcular los ingresos por marketplace
        results_data_list = results_data[['date', 'income', 'expense', 'benefit']].to_dict(orient='records')

        marketplaces = copied_df['invoice__marketplace__description'].dropna().unique()
        total_income = ingresostotalbase

        marketplace_data = []
        for marketplace in marketplaces:
            income = sales_df[sales_df['invoice__marketplace__description'] == marketplace]['income'].sum().round(2)
            percentage = round(income / total_income * 100, 2) if total_income > 0 else 0

            # truncar marketplace si es mayor a 15 caracteres
            if len(marketplace) > 15:
                marketplace = marketplace[:15] + "..."

            marketplace_data.append({
                "name": marketplace,
                "income": round(income, 2),
                "percentage": percentage
            })

        # Ordenar el array marketplace_data de mayor a menor por el campo income
        marketplace_data = sorted(marketplace_data, key=lambda x: x["income"], reverse=True)

        # Agrupar los marketplaces que estén en la posición 15 o superior en un marketplace llamado "otros"
        if len(marketplace_data) > 14:
            other_income = sum([m["income"] for m in marketplace_data[14:]])
            other_percentage = round(sum([m["percentage"] for m in marketplace_data[14:]]), 2)
            marketplace_data = marketplace_data[:14] + [
                {"name": "otros", "income": round(other_income, 2), "percentage": other_percentage}]

        # Agregar el marketplace "Ventas sin marketplace" para las ventas que no tengan marketplace
        qsm = sales_df[sales_df['invoice__marketplace__description'].isnull()]
        if not qsm.empty:
            income = qsm['income'].sum().round(2)
            percentage = round(income / total_income * 100, 2) if total_income > 0 else 0
            marketplace_data.append({
                "name": "Ventas sin marketplace",
                "income": round(income, 2),
                "percentage": percentage
            })

        marketplace_data_json = json.dumps(marketplace_data, ensure_ascii=False)

        income_stats.update({
            'grouping': grouping,
            'ingresosbase':format_decimal(ingresostotalbase, locale='es_ES') if ingresostotalbase else 0,
            'ingresosgraf': ingresostotalbase,
            'ingresosiva': format_decimal(ingresostotaliva, locale='es_ES') if ingresostotaliva else 0,
            'ingresosivafloat': ingresostotaliva,
            'ingresosirpf': format_decimal(ingresostotalirpf, locale='es_ES') if ingresostotalirpf else 0,
            'ingresosirpffloat': ingresostotalirpf,
            'marketplace_data': marketplace_data_json,
            'results_data': results_data_list
        })

        del sales_df, copied_df, results_data, marketplaces, marketplace_data

        return income_stats

    def process_providers_data(self, concepts_df: pd.DataFrame):
        """Procesar los datos de los proveedores usando un DataFrame"""
        expenses_stats = {}

        # Filtrar solo las facturas revisadas y agregar la columna de fecha
        expenses_df = concepts_df[concepts_df['invoice__invoice_category__code'] == 'expenses'].copy()
        expenses_df['invoice__accounting_date'] = pd.to_datetime(expenses_df['invoice__accounting_date'], errors='coerce')
        expenses_df.dropna(subset=['invoice__accounting_date'], inplace=True)

        is_txt_amz = expenses_df['invoice__is_txt_amz']
        is_import_dua = expenses_df['invoice__transaction_type__code'].str.contains('import-dua', na=False)

        # remover las facturas de importación solo para calcular las bases
        expenses_df['expense'] = np.where(
            ~is_import_dua,
            np.where(
                is_txt_amz,
                expenses_df['amount_euros'],
                expenses_df['amount_euros'] * expenses_df['quantity']
            ), 0
        )

        expenses_df['vat'] = np.where(
            is_import_dua,
            np.where(
                is_txt_amz,
                expenses_df['amount_original'] * expenses_df['vat'] / 100,
                expenses_df['amount_original'] * expenses_df['quantity'] * expenses_df['vat'] / 100
            ),
            np.where(
                is_txt_amz,
                expenses_df['amount_euros'] * expenses_df['vat'] / 100,
                expenses_df['amount_euros'] * expenses_df['quantity'] * expenses_df['vat'] / 100
            )
        )

        expenses_df['irpf'] = np.where(
            is_txt_amz,
            expenses_df['amount_euros'] * expenses_df['irpf'] / 100,
            expenses_df['amount_euros'] * expenses_df['quantity'] * expenses_df['irpf'] / 100
        )

        # Calcular los gastos agrupados por proveedor
        gastostotalbase = expenses_df['expense'].sum().round(2)
        gastostotaliva = expenses_df['vat'].sum().round(2)
        gastostotalirpf = expenses_df['irpf'].sum().round(2)

        # grouping para mostrar los datos en el gráfico por dia
        gastospordia = (
            expenses_df.groupby('invoice__accounting_date')['amount_euros']
            .sum()
            .reset_index()
            .round(2)
        )
        gastospordia['date'] = gastospordia['invoice__accounting_date'].dt.date.astype(str)
        gastospordia['income'] = 0
        gastospordia['expense'] = gastospordia['amount_euros']
        gastospordia['benefit'] = -gastospordia['amount_euros']
        results_data_list = gastospordia[['date', 'income', 'expense', 'benefit']].to_dict(orient='records')

        providers = expenses_df['invoice__provider__name'].dropna().unique()
        total_expense = gastostotalbase

        providers_data = []
        for prov in providers:
            prov_expense = expenses_df[expenses_df['invoice__provider__name'] == prov]['expense'].sum().round(2)
            percentage = round(prov_expense / total_expense * 100, 2) if total_expense > 0 else 0

            # truncar el nombre del proveedor si es mayor a 15 caracteres
            if len(prov) > 15:
                prov = prov[:15] + "..."
            providers_data.append({
                "name": prov,
                "expenses": round(prov_expense, 2),
                "percentage": percentage
            })

        # Ordenar el array providers_data de mayor a menor por el campo expense
        providers_data = sorted(providers_data, key=lambda x: x["expenses"], reverse=True)

        # Agrupar los proveedores que estén en la posición 15 o superior en un proveedor llamado "otros"
        if len(providers_data) > 14:
            other_expenses = sum([p["expenses"] for p in providers_data[14:]])
            other_percentage = round(sum([p["percentage"] for p in providers_data[14:]]), 2)
            providers_data = providers_data[:14] + [
                {"name": "otros", "expenses": round(other_expenses, 2), "percentage": other_percentage}]

        # convertir a json
        providers_data_json = json.dumps(providers_data, ensure_ascii=False)

        expenses_stats.update({
            'gastosbase': format_decimal(gastostotalbase, locale='es_ES') if gastostotalbase else 0,
            'gastosgraf': gastostotalbase * -1,
            'gastosiva': format_decimal(gastostotaliva, locale='es_ES') if gastostotaliva else 0,
            'gastosivafloat': gastostotaliva * -1,
            'gastosirpf': format_decimal(gastostotalirpf, locale='es_ES') if gastostotalirpf else 0,
            'gastosirpffloat': gastostotalirpf * -1,
            'providers_data': providers_data_json,
            'results_data': results_data_list
        })

        del expenses_df, gastospordia, providers, providers_data

        return expenses_stats

    def handle_no_permission(self):
        return HttpResponseRedirect(reverse("home"))

    def generate_invoice_stats_title(self, year, month):
        """Generar título para la sección de estado de facturas basado en los filtros aplicados"""
        if year == "all":
            return _("Estado Global de Facturas")

        # Mapeo de meses y trimestres
        month_names = {
            "01": _("Enero"), "02": _("Febrero"), "03": _("Marzo"),
            "04": _("Abril"), "05": _("Mayo"), "06": _("Junio"),
            "07": _("Julio"), "08": _("Agosto"), "09": _("Septiembre"),
            "10": _("Octubre"), "11": _("Noviembre"), "12": _("Diciembre"),
            "tri1": _("1er Trimestre"), "tri2": _("2º Trimestre"),
            "tri3": _("3er Trimestre"), "tri4": _("4º Trimestre")
        }

        if month == "all":
            return _("Estado de Facturas • Año %(year)s") % {'year': year}

        month_str = month_names.get(month, month)
        return _("Estado de Facturas • %(month)s %(year)s") % {
            'month': month_str, 
            'year': year
        }

class UserProfileView(LoginRequiredMixin, IsUserUsernamePermission, DetailView):
    model = User
    slug_field = "username"
    slug_url_kwarg = "username"

    # form_class = UserChangeForm

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        user = self.object
        # Asegurar obtener el seller más actualizado posible
        seller = Seller.objects.get(user=user)
        invoices = Invoice.objects.all().filter(seller_id=seller.id)

        # Añadir el objeto seller al contexto para que esté disponible en la plantilla
        context['seller'] = seller

        # Obtener el año actual para filtrar las facturas
        current_year = datetime.now().year

        # Generar título indicando el año actual
        context['invoice_stats_period_label'] = f"Estado de Facturas (Año {current_year})"

        # Datos de facturas
        invoice_data = {
            'num_sales_total': 0,
            'num_sales_pending': 0,
            'num_sales_revised': 0,
            'num_sales_discarded': 0,
            'percentage_sales_pending': 0,
            'percentage_sales_revised': 0,
            'percentage_sales_discarded': 0,

            'num_expenses_total': 0,
            'num_expenses_pending': 0,
            'num_expenses_revised': 0,
            'num_expenses_discarded': 0,
            'percentage_expenses_pending': 0,
            'percentage_expenses_revised': 0,
            'percentage_expenses_discarded': 0,

            'num_grand_total': 0,
            'num_grand_total_pending': 0,
            'num_grand_total_revised': 0,
            'num_grand_total_discarded': 0,
            'percentage_grand_total_pending': 0,
            'percentage_grand_total_revised': 0,
            'percentage_grand_total_discarded': 0,
        }

        if seller:
            # Consulta base: facturas del seller, filtrado por el año actual
            all_invoices_base_query = Invoice.objects.filter(seller=seller, accounting_date__year=current_year)
            pending_statuses = ['pending', 'revision-pending']
            revised_status = 'revised'
            discard_status = 'discard'

            # Facturas de Venta
            sales_invoices_query = all_invoices_base_query.filter(invoice_category__code='sales')
            invoice_data['num_sales_total'] = sales_invoices_query.count()
            if invoice_data['num_sales_total'] > 0:
                invoice_data['num_sales_pending'] = sales_invoices_query.filter(status__code__in=pending_statuses).count()
                invoice_data['num_sales_revised'] = sales_invoices_query.filter(status__code=revised_status).count()
                invoice_data['num_sales_discarded'] = sales_invoices_query.filter(status__code=discard_status).count()

                invoice_data['percentage_sales_pending'] = int((invoice_data['num_sales_pending'] / invoice_data['num_sales_total']) * 100)
                invoice_data['percentage_sales_revised'] = int((invoice_data['num_sales_revised'] / invoice_data['num_sales_total']) * 100)
                invoice_data['percentage_sales_discarded'] = int((invoice_data['num_sales_discarded'] / invoice_data['num_sales_total']) * 100)

            # Facturas de Gasto
            expenses_invoices_query = all_invoices_base_query.filter(invoice_category__code='expenses')
            invoice_data['num_expenses_total'] = expenses_invoices_query.count()
            if invoice_data['num_expenses_total'] > 0:
                invoice_data['num_expenses_pending'] = expenses_invoices_query.filter(status__code__in=pending_statuses).count()
                invoice_data['num_expenses_revised'] = expenses_invoices_query.filter(status__code=revised_status).count()
                invoice_data['num_expenses_discarded'] = expenses_invoices_query.filter(status__code=discard_status).count()

                invoice_data['percentage_expenses_pending'] = int((invoice_data['num_expenses_pending'] / invoice_data['num_expenses_total']) * 100)
                invoice_data['percentage_expenses_revised'] = int((invoice_data['num_expenses_revised'] / invoice_data['num_expenses_total']) * 100)
                invoice_data['percentage_expenses_discarded'] = int((invoice_data['num_expenses_discarded'] / invoice_data['num_expenses_total']) * 100)

            # Totales Generales (Ventas + Gastos)
            relevant_invoices_query = all_invoices_base_query.filter(invoice_category__code__in=['sales', 'expenses'])
            invoice_data['num_grand_total'] = relevant_invoices_query.count()
            if invoice_data['num_grand_total'] > 0:
                invoice_data['num_grand_total_pending'] = relevant_invoices_query.filter(status__code__in=pending_statuses).count()
                invoice_data['num_grand_total_revised'] = relevant_invoices_query.filter(status__code=revised_status).count()
                invoice_data['num_grand_total_discarded'] = relevant_invoices_query.filter(status__code=discard_status).count()

                invoice_data['percentage_grand_total_pending'] = int((invoice_data['num_grand_total_pending'] / invoice_data['num_grand_total']) * 100)
                invoice_data['percentage_grand_total_revised'] = int((invoice_data['num_grand_total_revised'] / invoice_data['num_grand_total']) * 100)
                invoice_data['percentage_grand_total_discarded'] = int((invoice_data['num_grand_total_discarded'] / invoice_data['num_grand_total']) * 100)

        context['invoice_data'] = invoice_data
        context['seller_vat'] = SellerVat.objects.all().filter(seller_id=seller.id)
        context['seller_vat_count'] = SellerVat.objects.all().filter(seller_id=seller.id).count()
        context['customers_count'] = Customer.objects.all().filter(seller_id=seller.id).count()
        context['providers_count'] = Provider.objects.all().filter(seller_id=seller.id).count()
        context['partners_count'] = Partner.objects.all().filter(seller_id=seller.id).count()
        context['workers_count'] = Worker.objects.all().filter(seller_id=seller.id).count()
        context['invoices_count'] = Invoice.objects.all().filter(seller_id=seller.id).count()

        context['formUser'] = UserChangeForm(instance=user)
        context['formSeller'] = sellerChangeForm(instance=seller)
        context['formSellerAddress'] = sellerAddressChangeForm(instance=seller)
        context['formSellerBank'] = sellerBankChangeForm(instance=seller)
        context['formPassword'] = PasswordChangeForm(user=user)
        context['next'] = self.request.GET.get('next', '')

        return context

    def post(self, request, *args, **kwargs):
        self.object = self.get_object()
        user = self.object
        seller = user.seller

        formUser = UserChangeForm(request.POST, instance=user)
        formSeller = sellerChangeForm(request.POST, request.FILES, instance=seller)
        formSellerAddress = sellerAddressChangeForm(request.POST, instance=seller)
        formSellerBank = sellerBankChangeForm(request.POST, instance=seller)
        formPassword = PasswordChangeForm(user=user, data=request.POST)

        current_tab = request.POST.get('current_tab', 'seller')
        next_url = request.POST.get('next', '')

        # 🟢 Nueva lógica para manejar el cambio de idioma
        if 'submit_language' in request.POST:
            language = request.POST.get('language')
            if language in dict(User._meta.get_field('language').choices).keys():
                user.language = language
                user.save()
                messages.success(request, "Idioma actualizado correctamente.")
            else:
                messages.error(request, "Idioma seleccionado no válido.")
            return redirect('users:profile', username=user.username)

        if 'submit_user' in request.POST and formUser.is_valid():
            formUser.save()
            return redirect('users:profile', username=user.username)

        if 'submit_seller' in request.POST:
            if formSeller.is_valid():
                formSeller.save()
                return redirect(next_url or 'users:profile', username=user.username)
            else:
                return self.render_form_with_errors(formSeller=formSeller, current_tab=current_tab, next_url=next_url)

        if 'submit_address' in request.POST and formSellerAddress.is_valid():
            formSellerAddress.save()
            return redirect('users:profile', username=user.username)

        if 'submit_bank' in request.POST and formSellerBank.is_valid():
            formSellerBank.save()
            return redirect('users:profile', username=user.username)

        if 'submit_password' in request.POST and formPassword.is_valid():
            formPassword.save()
            update_session_auth_hash(request, formPassword.user)
            return redirect('users:profile', username=user.username)

        return redirect('users:profile', username=user.username)

    def render_form_with_errors(self, current_tab, next_url, **forms_with_errors):
        context = self.get_context_data()
        context.update(forms_with_errors)
        context['current_tab'] = current_tab
        context['next'] = next_url
        return self.render_to_response(context)

    def handle_no_permission(self):
        return HttpResponseRedirect(reverse("home"))

class UserUpdateView(LoginRequiredMixin, SuccessMessageMixin, UpdateView):
    model = User
    fields = ["name"]
    success_message = _("Information successfully updated")

    def get_success_url(self):
        assert (
            self.request.user.is_authenticated
        )  # for mypy to know that the user is authenticated
        return self.request.user.get_absolute_url()

    def get_object(self):
        return self.request.user

    def handle_no_permission(self):
        return HttpResponseRedirect(reverse("home"))

class UserRedirectView(LoginRequiredMixin, RedirectView):
    permanent = False

    def get_redirect_url(self):
        return reverse("users:profile", kwargs={"username": self.request.user.username})

    def handle_no_permission(self):
        return HttpResponseRedirect(reverse("home"))


class PixelView(View):

    def get(self, request, *args, **kwargs):
        relative_path = '/static/assets/images/1x1.png'
        absolute_path = 'https://app.muaytax.com' + relative_path
        image_data = None

        # Busca la imagen en el proyecto y la lee como fichero
        if image_data is None:
            try:
                # Abre y lee el archivo de imagen
                with open(relative_path, 'rb') as image_file:
                    image_data = image_file.read()
            except Exception as e:
                image_data = None

        # Si no se encuentra la imagen en el proyecto, se intenta descargar de la URL Relativa
        if image_data is None:
            try:
                url = relative_path
                response = requests.get(url)
                if response.status_code == 200:
                    image_data = response.content
            except Exception as e:
                time.sleep(random.uniform(1, 5))
                image_data = None

        # Si no se encuentra la imagen en la URL Relativa, se intenta descargar de la URL Absoluta
        if image_data is None:
            url = absolute_path
            response = requests.get(url)
            if response.status_code == 200:
                image_data = response.content

        seller_id = kwargs.get('user')
        process_id = kwargs.get('process')
        seller = Seller.objects.get(id=seller_id)
        user = seller.user
        user_id = user.id
        # print("====================>>  User: " + str(user) + " process_id: " + str(process_id))
        Sellerprocess = SellerProcess.objects.filter(id=process_id, seller=seller)
        # print("====================>>  SellerProcess: " + str(Sellerprocess) + " process_id: " + str(process_id))
        if Sellerprocess is not None and Sellerprocess.exists():
            Sellerprocess = Sellerprocess.first()
            Sellerprocess.email_first_opening_date = datetime.now()
            # print("====================>> Fecha actual: " + str(datetime.now()))
            Sellerprocess.save()
            # print("====================>>  sellervat.email_first_opening_date: " + str( Sellerprocess.email_first_opening_date) + " process_id: " + str(process_id))
        return HttpResponse(image_data, content_type="image/png")


class WelcomePixelView(View):

    def get(self, request, *args, **kwargs):
        relative_path = '/static/assets/images/1x1.png'
        absolute_path = 'https://app.muaytax.com' + relative_path
        image_data = None

        # Busca la imagen en el proyecto y la lee como fichero
        if image_data is None:
            try:
                # Abre y lee el archivo de imagen
                with open(relative_path, 'rb') as image_file:
                    image_data = image_file.read()
            except Exception as e:
                image_data = None

        # Si no se encuentra la imagen en el proyecto, se intenta descargar de la URL Relativa
        if image_data is None:
            try:
                url = relative_path
                response = requests.get(url)
                if response.status_code == 200:
                    image_data = response.content
            except Exception as e:
                time.sleep(random.uniform(1, 5))
                image_data = None

        # Si no se encuentra la imagen en la URL Relativa, se intenta descargar de la URL Absoluta
        if image_data is None:
            url = absolute_path
            response = requests.get(url)
            if response.status_code == 200:
                image_data = response.content

        seller_id = kwargs.get('user')
        process_id = kwargs.get('process')
        seller = Seller.objects.get(id=seller_id)
        user = seller.user
        user_id = user.id
        #  print("====================>>  User: " + str(user) + " process_id: " + str(process_id))
        Sellerprocess = SellerProcess.objects.filter(id=process_id, seller=seller)
        # print("====================>>  Sellerprocess: " + str(Sellerprocess) + " process_id: " + str(process_id))
        fecha_hora_actual = datetime.now()
        fecha_hora_actual_con_timezone = timezone.make_aware(fecha_hora_actual, timezone.get_current_timezone())
        if Sellerprocess is not None and Sellerprocess.exists():
            Sellerprocess = Sellerprocess.first()
            Sellerprocess.email_welcome_opening_date = fecha_hora_actual_con_timezone
            # print("====================>> Fecha actual: " + str(datetime.now()))
            Sellerprocess.save()
        # print("====================>>  Sellerprocess.email_welcome_opening_date: " + str(Sellerprocess.email_welcome_opening_date) + " process_id: " + str(process_id))

        # to_email = ['']
        # reply_to = ['']
        # message = "mail de aviso welcome apertura" + str(user_id) + ' y process_id: ' + str(process_id) + " " + str(
        #     Sellerprocess.email_welcome_opening_date)
        # subject = 'mail abierto welcome por user: ' + str(user_id) + ' y process_id: ' + str(process_id)
        # text_content = 'mail abierto'
        # from_email = '<EMAIL>'
        # html_content = message
        # tracked_email = EmailMultiAlternatives(subject, text_content, from_email, to_email, reply_to=reply_to,
        #                                        headers={"Message-ID": "foo"})
        # tracked_email.attach_alternative(html_content, "text/html")
        # tracked_email.send()

        # print("====================>>  User ha abierto el mail welcome, user_id: " + str(user_id) + " process_id: " + str(process_id))

        return HttpResponse(image_data, content_type="image/png")

@require_POST
def set_language(request):
    language = request.POST.get('language', settings.LANGUAGE_CODE)
    next_url = request.POST.get('next', request.META.get('HTTP_REFERER', '/'))

    if language in [lang[0] for lang in settings.LANGUAGES]:
        # Si el usuario está autenticado, guardar preferencia en BD
        if request.user.is_authenticated:
            request.user.language = language
            request.user.save()

        # Activar el nuevo idioma
        translation.activate(language)
        request.session['django_language'] = language

        # Preparar la respuesta
        response = redirect(next_url)

        # Siempre establecer la cookie
        response.set_cookie(
            'preferred_language',
            language,
            max_age=365 * 24 * 60 * 60  # 1 año
        )

        return response
    else:
        messages.error(request, "Idioma seleccionado no válido.")

    return redirect(next_url)
