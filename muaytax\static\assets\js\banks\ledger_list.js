$(document).ready(function() {
    // Configuración
    const PAGE_SIZE = 50;
    const ENTRIES_PER_CHUNK = 50;
    const TIMEOUT_DELAY = 50;

    // ===== Funciones utilitarias =====

    function getCookie(name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }

    function formatCurrency(value) {
        if (value === null || typeof value === 'undefined') return '0,00 €';
        return value.toLocaleString('es-ES', { style: 'currency', currency: 'EUR' });
    }

    // ===== Configuración inicial =====
    const csrftoken = getCookie('csrftoken');

    // Configuración de AJAX con CSRF token
    $.ajaxSetup({
        beforeSend: function(xhr, settings) {
            if (!/^(GET|HEAD|OPTIONS|TRACE)$/i.test(settings.type) && !this.crossDomain) {
                xhr.setRequestHeader("X-CSRFToken", csrftoken);
            }
        }
    });

    // Variables de estado
    let currentPage = 0;
    let hasMoreData = true;
    let loadingData = false;
    let currentAccounts = [];
    let currentStartDate = '';
    let currentEndDate = '';
    let currentFilters = {
        text: { type: '', value: '', column: 'all' },
        amount: { type: '', value: '', column: 'debit' }
    };
    let filtersActive = false;

    // Inicializar estado de los controles de filtro
    disableAllFilterControls();

    // ===== Gestión del dropdown de cuentas =====

    // Actualizar resumen de selección
    function updateSelectionSummary() {
        const selectedCount = $('.account-checkbox:checked').length;
        $('#account-selection-summary').text(
            selectedCount === 0 ? gettext('Seleccionar cuentas') : `${selectedCount} ${gettext('cuenta(s) seleccionada(s)')}`
        );
    }

    // Inicializar resumen
    updateSelectionSummary();

    // Toggle del dropdown de cuentas
    $('#account-dropdown-button').on('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        $('#account-dropdown-menu').toggle();
    });

    // Cerrar dropdown al hacer click fuera
    $(document).on('click', function(e) {
        if (!$(e.target).closest('.account-dropdown').length) {
            $('#account-dropdown-menu').hide();
        }
    });

    // Evitar que los clicks dentro del dropdown lo cierren
    $('#account-dropdown-menu').on('click', function(e) {
        e.stopPropagation();
    });

    // Actualizar resumen al cambiar la selección
    $('.account-checkbox').on('change', updateSelectionSummary);

    // Botones para seleccionar/deseleccionar todos
    $('#select-all-accounts').on('click', function(e) {
        e.preventDefault();
        $('.account-checkbox').prop('checked', true);
        updateSelectionSummary();
    });

    $('#deselect-all-accounts').on('click', function(e) {
        e.preventDefault();
        $('.account-checkbox').prop('checked', false);
        updateSelectionSummary();
    });

    // ===== Gestión de filtros avanzados =====

    // Toggle para mostrar/ocultar filtros avanzados
    $('#toggle-advanced-filters').on('click', function() {
        $('#advancedFiltersCollapse').slideToggle(300);
    });

    // Activar/desactivar campos de valores y selectores de columna según tipo seleccionado
    $('#text-filter-type').on('change', function() {
        if (!filtersActive) return; // No hacer nada si los filtros están desactivados

        const isEnabled = $(this).val() !== '';
        $('#text-filter-value').prop('disabled', !isEnabled);
        $('#text-filter-column').prop('disabled', !isEnabled);
    });

    $('#amount-filter-type').on('change', function() {
        if (!filtersActive) return; // No hacer nada si los filtros están desactivados

        const isEnabled = $(this).val() !== '';
        $('#amount-filter-value').prop('disabled', !isEnabled);
        $('#amount-filter-column').prop('disabled', !isEnabled);
    });

    // Toggle para activar/desactivar los filtros
    $('#toggle-filters').on('click', function() {
        // Cambiar el estado de activación
        filtersActive = !filtersActive;
        $(this).attr('data-active', filtersActive);

        // Actualizar el aspecto visual del botón
        if (filtersActive) {
            $(this).removeClass('btn-outline-success').addClass('btn-success');
            $(this).find('i').removeClass('fa-toggle-off').addClass('fa-toggle-on');
            $(this).html('<i class="fas fa-toggle-on me-1"></i> ' + gettext('Filtro ACTIVADO'));

            // Habilitar los controles de filtro según su lógica actual
            enableFilterControls();

            // Si hay una búsqueda activa, aplicar los filtros inmediatamente
            if (currentAccounts.length > 0 && currentStartDate && currentEndDate) {
                updateFilters();
                loadLedgerData(currentAccounts, currentStartDate, currentEndDate, 0, true);
            }
        } else {
            $(this).removeClass('btn-success').addClass('btn-outline-success');
            $(this).find('i').removeClass('fa-toggle-on').addClass('fa-toggle-off');
            $(this).html('<i class="fas fa-toggle-off me-1"></i> ' + gettext('Filtro DESACTIVADO'));

            // Deshabilitar todos los controles de filtro
            disableAllFilterControls();

            // Limpiar los filtros actuales
            currentFilters = {
                text: { type: '', value: '', column: 'all' },
                amount: { type: '', value: '', column: 'debit' }
            };

            // Si hay una búsqueda activa, recargar sin filtros
            if (currentAccounts.length > 0 && currentStartDate && currentEndDate) {
                loadLedgerData(currentAccounts, currentStartDate, currentEndDate, 0, true);
            }
        }
    });

    // Función para deshabilitar todos los controles de filtro
    function disableAllFilterControls() {
        // Deshabilitar todos los controles de filtro de texto
        $('#text-filter-type, #text-filter-value, #text-filter-column').prop('disabled', true);

        // Deshabilitar todos los controles de filtro de cantidad
        $('#amount-filter-type, #amount-filter-value, #amount-filter-column').prop('disabled', true);
    }

    // Función para habilitar los controles de filtro según la lógica existente
    function enableFilterControls() {
        // Re-habilitar los selectores de tipo
        $('#text-filter-type, #amount-filter-type').prop('disabled', false);

        // Aplicar la lógica de habilitación según valores seleccionados
        const textTypeEnabled = $('#text-filter-type').val() !== '';
        $('#text-filter-value, #text-filter-column').prop('disabled', !textTypeEnabled);

        const amountTypeEnabled = $('#amount-filter-type').val() !== '';
        $('#amount-filter-value, #amount-filter-column').prop('disabled', !amountTypeEnabled);
    }

    // Actualizar objeto de filtros
    function updateFilters() {
        // Si los filtros están desactivados, no actualizar los valores
        if (!filtersActive) {
            currentFilters = {
                text: { type: '', value: '', column: 'all' },
                amount: { type: '', value: '', column: 'debit' }
            };
            return;
        }

        // Actualizar los filtros con los valores de los controles
        currentFilters = {
            text: {
                type: $('#text-filter-type').val(),
                value: $('#text-filter-value').val(),
                column: $('#text-filter-column').val()
            },
            amount: {
                type: $('#amount-filter-type').val(),
                value: $('#amount-filter-value').val(),
                column: $('#amount-filter-column').val()
            }
        };
    }

    // ===== Configuración de DateRangePicker =====
    $('#date-range').daterangepicker({
        startDate: moment().startOf('month'),
        endDate: moment().endOf('month'),
        locale: {
            format: 'DD/MM/YYYY',
            applyLabel: gettext('Aplicar'),
            cancelLabel: gettext('Cancelar'),
            fromLabel: gettext('Desde'),
            toLabel: gettext('Hasta'),
            customRangeLabel: gettext('Rango personalizado'),
            daysOfWeek: [gettext('Do'), gettext('Lu'), gettext('Ma'), gettext('Mi'), gettext('Ju'), gettext('Vi'), gettext('Sa')],
            monthNames: [gettext('Enero'), gettext('Febrero'), gettext('Marzo'), gettext('Abril'), gettext('Mayo'), gettext('Junio'), gettext('Julio'), gettext('Agosto'), gettext('Septiembre'), gettext('Octubre'), gettext('Noviembre'), gettext('Diciembre')]
        }
    });

    // ===== Carga y procesamiento de datos =====

    // Función principal para cargar datos
    function loadLedgerData(accounts, startDate, endDate, page = 0, reset = true) {
        if (loadingData) return;

        loadingData = true;
        $('#loading-indicator').show();

        if (reset) {
            $('#ledger-table tbody').empty();
            currentPage = 0;
            hasMoreData = true;
        }

        const ajaxUrl = $('#ledger-table').data('ajax-url') || window.LEDGER_AJAX_URL;

        // Datos para la petición AJAX
        const requestData = {
            'account': accounts,
            'start_date': startDate,
            'end_date': endDate,
            'start': page * PAGE_SIZE,
            'length': PAGE_SIZE,
            'draw': currentPage + 1,
            'csrfmiddlewaretoken': csrftoken
        };

        // Añadir filtros si están activos y definidos
        if (filtersActive) {
            if (currentFilters.text.type) {
                requestData['text_filter_type'] = currentFilters.text.type;
                requestData['text_filter_value'] = currentFilters.text.value;
                requestData['text_filter_column'] = currentFilters.text.column;
            }

            if (currentFilters.amount.type) {
                requestData['amount_filter_type'] = currentFilters.amount.type;
                requestData['amount_filter_value'] = currentFilters.amount.value;
                requestData['amount_filter_column'] = currentFilters.amount.column;
            }
        }

        // Permitir que el navegador pinte el loader antes de la petición AJAX
        setTimeout(function() {
            $.ajax({
                url: ajaxUrl,
                type: 'POST',
                data: requestData,
                traditional: true,
                success: function(json) {
                    handleDataSuccess(json, accounts, startDate, endDate, page, reset);
                },
                error: function(xhr) {
                    $('#loading-indicator').hide();
                    alert(gettext('Error al cargar los datos: ') + (xhr.responseJSON ? xhr.responseJSON.error : gettext('Error desconocido')));
                    loadingData = false;
                }
            });
        }, TIMEOUT_DELAY);
    }

    // Manejo de respuesta exitosa
    function handleDataSuccess(json, accounts, startDate, endDate, page, reset) {
        const $tbody = $('#ledger-table tbody');
        if (reset) { $tbody.empty(); }

        let hayMovimientosGeneral = false;

        // Iniciar procesamiento secuencial de cuentas
        if (json.data && json.data.length > 0) {
            processAccountsSequentially(json.data, 0, json, hayMovimientosGeneral);
        } else {
            handleEmptyData(json, reset);
        }

        // Actualizar estado global
        currentAccounts = accounts;
        currentStartDate = startDate;
        currentEndDate = endDate;
    }

    // Manejo de datos vacíos
    function handleEmptyData(json, reset) {
        const $tbody = $('#ledger-table tbody');

        if (reset) {
            $tbody.html('<tr><td colspan="7" class="text-center text-muted">' + gettext('No hay datos para mostrar con los filtros seleccionados.') + '</td></tr>');
        }

        updateTotals(json);
        finalizePagination(json);
    }

    // Actualización de totales en la interfaz
    function updateTotals(json) {
        $('#total-debit').text(formatCurrency(json.grand_total_debit));
        $('#total-credit').text(formatCurrency(json.grand_total_credit));
        $('#final-balance').text(formatCurrency(json.grand_final_balance));
    }

    // Actualización de paginación
    function finalizePagination(json) {
        currentPage++;
        hasMoreData = json.recordsFiltered > (currentPage * PAGE_SIZE);
        $('#load-more').toggle(hasMoreData);

        $('#loading-indicator').hide();
        loadingData = false;
    }

    // Procesamiento secuencial de cuentas
    function processAccountsSequentially(accountsData, index, json, hayMovimientosGeneral) {
        const $tbody = $('#ledger-table tbody');

        if (index >= accountsData.length) {
            // Todas las cuentas procesadas, finalizar página
            if (currentPage === 0) {
                appendGrandTotals(json, hayMovimientosGeneral, accountsData.length);
            }

            updateTotals(json);
            finalizePagination(json);
            return;
        }

        // Procesar cuenta actual
        const accountData = accountsData[index];
        const accountHtmlAccumulator = {
            headerHtml: createAccountHeader(accountData),
            entriesHtml: '',
            callback: function() {
                // Este callback se llama cuando todas las entradas de la cuenta actual están listas
                setTimeout(function() {
                    processAccountsSequentially(accountsData, index + 1, json, hayMovimientosGeneral);
                }, 0);
            }
        };

        if (accountData.entries && accountData.entries.length > 0) {
            hayMovimientosGeneral = true;
            // Iniciar procesamiento de entradas para esta cuenta
            processEntriesSequentially(accountData, accountData.entries, 0, accountHtmlAccumulator);
        } else {
            // Sin entradas, finalizar esta cuenta inmediatamente
            processEntriesSequentially(accountData, [], 0, accountHtmlAccumulator);
        }
    }

    // Crear cabecera HTML para una cuenta
    function createAccountHeader(accountData) {
        return '<tr class="table-primary"><td colspan="7"><b>' + accountData.account_name + '</b></td></tr>' +
               '<tr><td colspan="4">' + gettext('Saldo anterior') + '</td><td colspan="3" class="text-right">' +
               formatCurrency(accountData.previous_balance) + '</td></tr>';
    }

    // Agregar totales generales
    function appendGrandTotals(json, hayMovimientosGeneral, accountsCount) {
        const $tbody = $('#ledger-table tbody');
        let grandTotalHtml = '<tr class="table-dark text-bold"><td colspan="4">' + gettext('Saldo total') + '</td>' +
            '<td class="text-right">' + formatCurrency(json.grand_total_debit) + '</td>' +
            '<td class="text-right">' + formatCurrency(json.grand_total_credit) + '</td>' +
            '<td class="text-right">' + formatCurrency(json.grand_final_balance) + '</td></tr>';

        if (!hayMovimientosGeneral && accountsCount > 0) {
            grandTotalHtml += '<tr><td colspan="7" class="text-center text-danger">' +
                             gettext('No existen movimientos en el periodo seleccionado para ninguna de las cuentas') + '</td></tr>';
        }

        $tbody.append(grandTotalHtml);
    }

    // Procesamiento secuencial de entradas por cuenta
    function processEntriesSequentially(accountData, entries, entryIndex, accountHtmlAccumulator) {
        const $tbody = $('#ledger-table tbody');
        const chunkEnd = Math.min(entryIndex + ENTRIES_PER_CHUNK, entries.length);
        let entriesHtml = '';

        // Procesar este lote de entradas
        for (let i = entryIndex; i < chunkEnd; i++) {
            const entry = entries[i];
            entriesHtml += createEntryRow(entry);
        }

        // Acumular el HTML de entradas
        accountHtmlAccumulator.entriesHtml += entriesHtml;

        if (chunkEnd < entries.length) {
            // Más entradas para procesar, programar el siguiente lote
            setTimeout(function() {
                processEntriesSequentially(accountData, entries, chunkEnd, accountHtmlAccumulator);
            }, 0);
        } else {
            // Todas las entradas procesadas, finalizar HTML de la cuenta
            finalizeAccountHtml(accountData, entries, accountHtmlAccumulator);
        }
    }

    // Crear fila HTML para una entrada
    function createEntryRow(entry) {
        return '<tr>' +
            '<td>' + entry.date + '</td>' +
            '<td>' + entry.num + '</td>' +
            '<td>' + entry.document + '</td>' +
            '<td>' + entry.concept + '</td>' +
            '<td class="text-right">' + formatCurrency(entry.debit) + '</td>' +
            '<td class="text-right">' + formatCurrency(entry.credit) + '</td>' +
            '<td class="text-right">' + formatCurrency(entry.balance) + '</td>' +
        '</tr>';
    }

    // Finalizar HTML de una cuenta
    function finalizeAccountHtml(accountData, entries, accountHtmlAccumulator) {
        const $tbody = $('#ledger-table tbody');
        let finalAccountHtml = accountHtmlAccumulator.headerHtml + accountHtmlAccumulator.entriesHtml;

        if (entries.length === 0) {
            finalAccountHtml += '<tr><td colspan="7" class="text-center text-muted">' +
                               gettext('Sin movimientos en el periodo seleccionado para esta cuenta') + '</td></tr>';
        }

        // Agregar totales de la cuenta
        finalAccountHtml += '<tr class="table-secondary text-bold"><td colspan="4">' + gettext('Saldo de la cuenta') + '</td>' +
            '<td class="text-right">' + formatCurrency(accountData.total_debit) + '</td>' +
            '<td class="text-right">' + formatCurrency(accountData.total_credit) + '</td>' +
            '<td class="text-right">' + formatCurrency(accountData.final_balance) + '</td></tr>';

        // Agregar separador
        finalAccountHtml += '<tr><td colspan="7" style="padding: 15px;"></td></tr>';

        $tbody.append(finalAccountHtml);

        // Señalar que esta cuenta está procesada
        accountHtmlAccumulator.callback();
    }

    // ===== Manejadores de eventos =====

    $('#search-button').on('click', function(e) {
        e.preventDefault();

        const accounts = getSelectedAccounts();
        const dateRange = $('#date-range').val();

        if (accounts.length > 0 && dateRange) {
            const dates = dateRange.split(' - ');
            $('#account-dropdown-menu').hide();

            // Actualizar filtros antes de buscar (solo si están activos)
            updateFilters();

            loadLedgerData(accounts, dates[0], dates[1], 0, true);
        } else {
            alert(gettext('Por favor, seleccione al menos una cuenta y un rango de fechas'));
        }
        return false;
    });

    $('#load-more').on('click', function() {
        if (!loadingData && hasMoreData) {
            loadLedgerData(currentAccounts, currentStartDate, currentEndDate, currentPage, false);
        }
    });

    $('#export-csv').on('click', function() {
        const accounts = getSelectedAccounts();
        const dateRange = $('#date-range').val();

        if (!accounts.length || !dateRange) {
            alert(gettext('Por favor, seleccione al menos una cuenta y un rango de fechas'));
            return;
        }

        const dates = dateRange.split(' - ');
        const params = {
            account: JSON.stringify(accounts),
            start_date: dates[0],
            end_date: dates[1],
            format: 'csv'
        };

        // Añadir parámetros de filtro a la URL de exportación (solo si están activos)
        if (filtersActive) {
            if (currentFilters.text.type) {
                params.text_filter_type = currentFilters.text.type;
                params.text_filter_value = currentFilters.text.value;
                params.text_filter_column = currentFilters.text.column;
            }

            if (currentFilters.amount.type) {
                params.amount_filter_type = currentFilters.amount.type;
                params.amount_filter_value = currentFilters.amount.value;
                params.amount_filter_column = currentFilters.amount.column;
            }
        }

        window.location.href = window.LEDGER_EXPORT_URL + "?" + $.param(params);
    });

    function getSelectedAccounts() {
        const accounts = [];
        $('.account-checkbox:checked').each(function() {
            accounts.push($(this).val());
        });
        return accounts;
    }
});
