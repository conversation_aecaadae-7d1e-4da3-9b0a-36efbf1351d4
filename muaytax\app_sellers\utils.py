import json
import os
import tempfile
from datetime import datetime

import pandas as pd
import pdfkit
from dateutil.relativedelta import relativedelta
from django.conf import settings
from django.core.exceptions import ValidationError
from django.core.mail import EmailMultiAlternatives
from django.db import connection
from django.db.models import Case, F, FloatField, Q, Sum, When
from django.template.loader import render_to_string
from django.utils import timezone
from django.utils.html import format_html

from muaytax.app_invoices.models import Invoice
from muaytax.app_sellers.notification import SellerNotification
from muaytax.dictionaries.models import Country
from muaytax.dictionaries.models.document_type import DocumentType
from muaytax.dictionaries.models.store_products import StoreProduct
from muaytax.utils.env_resources import logo_url_head_muaytax

debug = settings.DEBUG # Variable de depuración global

DOCUMENT_TYPE_JSON_PATH = "muaytax/dictionaries/data/document_type.json"

LIMIT_X_ENTITY = {
    'llc': 40,
    'self-employed': 50,
    'sl': 70
}

def update_limit_invoice(seller):
    seller.limit_invoice = get_limit_invoice(seller)
    seller.save()

def get_limit_invoice(seller):
    # if seller.user.date_joined.date() >= datetime(2024, 1, 1).date():
    #     limit = 0
    #     total_vats = seller.vat_seller.filter(is_contracted=True).count()
    #     is_contracted = seller.contracted_accounting
    #
    #     is_cond = all(seller.legal_entity != string for string in [None, 'llc', 'other', ''])
    #
    #     if total_vats > 0:
    #         if is_contracted and is_cond:
    #             limit = LIMIT_X_ENTITY[seller.legal_entity]
    #         limit = limit + total_vats * LIMIT_X_ENTITY['llc']
    #     else:
    #         if is_cond:
    #             limit = LIMIT_X_ENTITY[seller.legal_entity]
    #     return limit
    return seller.limit_invoice

def get_providers_model347(seller, accounting_year):
    sql = f"SELECT func_gets_providers_mod347({seller.id}, {accounting_year});"
    sql_result = ""
    result_list = []
    try:
        with connection.cursor() as cursor:
            cursor.execute(sql)
            row = cursor.fetchone()
            while row is not None:
                sql_result = row[0]
                if sql_result == None:
                    return result_list
                result_list = json.loads(sql_result)
                row = cursor.fetchone()
            # if row:
            #     sql_result = row[0]
            #     result_list = json.loads(sql_result)

    except Exception as e:
        debug and print("ERROR_P: ", e)

    return result_list

def get_customers_model347(seller, accounting_year):
    sql = f"SELECT func_gets_customers_mod347({seller.id}, {accounting_year});"
    sql_result = ""
    result_list = []
    try:
        with connection.cursor() as cursor:
            cursor.execute(sql)
            row = cursor.fetchone()
            while row is not None:
                sql_result = row[0]
                if sql_result == None:
                    return result_list
                result_list = json.loads(sql_result)
                row = cursor.fetchone()

    except Exception as e:
        debug and print("ERROR_C: ", e)

    return result_list

def get_total_sum_in_periods_model_347(declarado, seller, accounting_year):
    total_sum_periods = {}
    total_year = 0

    for i in range(1, 13, 3):
        start_date = datetime(int(accounting_year), i, 1)
        end_date = start_date + relativedelta(months=2, day=31)

        total_inv = Invoice.objects.filter(
            Q(provider__id=declarado['id']) | Q(customer__id=declarado['id']),
            seller=seller,
            expedition_date__range=[start_date, end_date],
            status__code='revised',
            tax_country__iso_code='ES'
        ).exclude(
            Q(transaction_type__code__contains='intra-community') |
            Q(transaction_type__code__contains='import') |
            Q(transaction_type__code__contains='transfer') |
            Q(transaction_type__code__contains='copy')
        ).aggregate(
            total_concepts_inv=Sum(
                Case(
                    When(
                        concept__irpf_euros=0,
                        then=F('concept__total_euros')
                    ),
                    default=0,
                    output_field=FloatField()
                )
            )
        )['total_concepts_inv']

        total_inv = round(total_inv, 2) if total_inv is not None else 0
        total_sum_periods[f't{i // 3 + 1}'] = total_inv
        total_year += total_inv

    total_sum_periods['total'] = round(total_year, 2)
    return total_sum_periods

def get_model347_carts(seller, declarados, accounting_year):
    """
    Declarados debe venir en formato lista, NO en queryset
    Esto es porque viene la union de los queries de custumers y providers
    """

    template_path = 'documents/cartas/carta_model347.html'
    today = timezone.now()

    temp_dir = tempfile.mkdtemp()
    pdf_files = []
    for index, declarado in enumerate(declarados):
        if declarado['country'] == 'ES':
            clave = declarado['clave']
            declarado['country'] = Country.objects.get(iso_code=declarado['country']).name if declarado[
                'country'] else None
            # yearly_values = get_total_sum_in_periods_model_347(declarado, seller, accounting_year)
            name_declarado = declarado['name'].replace(' ', '').lower()
            name_declarado = name_declarado.replace(',', '').replace('.', '')
            file_name = f"carta_{index + 1}_modelo347_{name_declarado}_{clave.replace(' ', '').replace('-', '_')}.pdf"

            # Datos para rellenar el template
            data = {
                "seller": seller,
                "client": declarado,
                "today": today,
                "year": accounting_year,
                # "yearly_values": yearly_values,
                "clave": clave,
            }

            # Renderizar el template con los datos
            rendered_template = render_to_string(template_path, data)

            # Ruta donde se guardará el PDF generado
            output_path = os.path.join(temp_dir, file_name)

            # Generar el PDF
            pdfkit.from_string(rendered_template, output_path)
            pdf_files.append(output_path)

    return pdf_files

def add_totals_per_period(declarados, seller, year):
    for declarado in declarados:
        total_invoices = get_total_sum_in_periods_model_347(declarado, seller, year)
        declarado.update({
            "t1": total_invoices['t1'],
            "t2": total_invoices['t2'],
            "t3": total_invoices['t3'],
            "t4": total_invoices['t4']
        })
    return declarados

def send_email_oss(sellervat):
    # si tiene la oss contratada y tiene el numero de iva
    if sellervat.seller.oss and sellervat.vat_number:
        # envia un email a gestoria
        email = '<EMAIL>'
        seller = sellervat.seller.name
        country = sellervat.vat_country.name
        iva_number = sellervat.vat_number
        fiscal_number = sellervat.steuernummer or sellervat.siret

        message = render_to_string("emails/sellervat_iva_oss_contracted.html", {
            'seller': seller,
            'country': country,
            'iva_number': iva_number,
            'fiscal_number': fiscal_number,
            'logo_head_muaytax': logo_url_head_muaytax()
        })
        subject = 'MUAYTAX - Aviso de nuevo IVA'
        from_email = '<EMAIL>'
        to_email = [email]
        reply_to = [email]
        html_content = message
        text_content = 'Aviso de nuevo IVA'
        bcc_email = ['<EMAIL>']
        email_iva_oss = EmailMultiAlternatives(subject, text_content, from_email, to_email, reply_to=reply_to, bcc=bcc_email)
        email_iva_oss.attach_alternative(html_content, "text/html")
        email_iva_oss.send()

def send_end_service_email(instance, changes_in_date, service_name, email_type="low"):
    debug and print(f'typof de email_tyepe es {type(email_type)}')
    email_services_type = 'Alta' if email_type == "high" else 'Baja'
    debug and print(f'******************email_services_type: {email_services_type}')
    subject = f'MUAYTAX - {email_services_type} de Servicios'
    from_email = '<EMAIL>'
    to_email = ['<EMAIL>']

    if not settings.IS_PRODUCTION:
        to_email = ['<EMAIL>']

    # Diccionario de equivalencias legal_entity
    legal_entity_map = {
        "self-employed": _("Autónomo"),
        "sl": "SL",
        "llc": "LLC",
        "other": _("Otro"),
    }

    # Obtener información básica de `seller` y `legal_entity`
    seller = instance.seller if hasattr(instance, 'seller') else instance
    legal_entity = seller.legal_entity if hasattr(seller, 'legal_entity') else ''

    # Usar el nombre completo si está disponible, si no, el `shortname`
    full_name = f"{seller.first_name} {seller.last_name}" if seller.first_name and seller.last_name else seller.shortname

    # Cabecera del correo según el tipo de operación
    header = f"{email_services_type} de Servicios"

    # Obtener país de IVA si existe
    vat_country = getattr(instance, 'vat_country', None)
    vat_country_name = vat_country.name if vat_country else None

    # Determinar si es una desactivación masiva
    is_deactivation = service_name == "Servicios desactivados"

    if email_services_type == "Alta":
        message_plain = (
            f"Desde el departamento de Gestoría se han realizado los siguientes cambios en las fechas de alta de los servicios contratados "
            f"para el cliente {full_name} con la empresa {seller} y entidad {legal_entity}:\n"
        )
    else:  # Caso de Baja
        message_plain = (
            f"Desde el departamento de Gestoría se han realizado los siguientes cambios en las fechas de baja de los servicios contratados "
            f"para el cliente {full_name} con la empresa {seller} y entidad {legal_entity}:\n"
        )

    if is_deactivation: # Caso de desactivación masiva de todos los servicios

        changes_by_country = {}
        for change in changes_in_date:
            country = change.get('country', vat_country_name)
            if country not in changes_by_country:
                changes_by_country[country] = []
            changes_by_country[country].append(change)

        # Generar mensaje agrupado por modelo y pais si existe
        for country, changes in changes_by_country.items():
            if country is None:
                group_header = "Servicios del Seller"
            elif country == 'not-assigned':
                group_header = "Otros servicios"
            else:
                group_header = f"Servicios del país IVA {country}"

            message_plain += f"\n- {group_header}:\n"
            for change in changes:
                new_date = change.get('new_date')
                message_plain += f"  - {change['label']}: a {new_date} (new)\n"

        debug and print(f'--------------->>>changes_by_country : {changes_by_country}')
        changes = changes_by_country

    else: # Caso de baja manual desde Seller o SellerVat

        group_header = f"Servicios del país IVA {vat_country_name}" if vat_country_name else "Servicios del Seller"
        message_plain += f"\n- {group_header}:\n"

        for change in changes_in_date:
            previous_date = change.get('previous_date')
            new_date = change.get('new_date')
            if previous_date:
                message_plain += f"  - {change['label']}: de {previous_date} a {new_date} (act)\n"
            else:
                message_plain += f"  - {change['label']}: a {new_date} (new)\n"
        debug and print(f'--------------->>>changes_in_date : {changes_in_date}')
        changes = changes_in_date

    # Convertir `legal_entity` a su equivalente legible
    legal_entity_display = legal_entity_map.get(legal_entity, '')
    debug and print(f'--------------->>>legal_entity_display : {legal_entity_display}')

    # Preparar datos para el template
    template_data = {
        'header': header,
        'changes': changes,
        'service_name': service_name,
        'full_name': full_name,
        'seller': seller,
        'legal_entity': legal_entity_display,
        'vat_country': vat_country_name,
        'logo_head_muaytax': logo_url_head_muaytax(),
        'is_deactivation': is_deactivation,
        'email_services_type': email_services_type
    }

    debug and print(f"Valores pasados al template 'end_date_services.html'::\n {json.dumps(template_data, default=str, indent=4)}")

    # Renderizar el template con los datos preparados
    html_content = render_to_string('emails/end_date_services.html', template_data)

    # Configurar y enviar el correo
    bcc_email = ['<EMAIL>', '<EMAIL>']
    email = EmailMultiAlternatives(subject, message_plain, from_email, to_email, bcc=bcc_email)
    email.attach_alternative(html_content, "text/html")
    email.send()

def update_send_email_for_limit_invoice(seller: object) -> None:
    """Actualiza el campo send_email_limit_invoice del SELLER y envía un correo si se cumple la condición."""
    # Calcular el número total de facturas asociadas a ese vendedor en el mes actual
    current_date = datetime.now()
    total_invoices = Invoice.objects.filter(
        seller_id=seller.id,
        created_at__year=current_date.year,
        created_at__month=current_date.month,
    ).exclude(
        Q(invoice_category__code__icontains='_copy') | Q(is_txt_amz=True) | Q(is_generated=True)
    ).count()

    # Obtener el límite de facturas del vendedor
    invoices_limit = seller.limit_invoice
    calculo_porciento = int((total_invoices / invoices_limit) * 100)

    email_sent = seller.send_email_limit_invoice

    # Verificar si el porcentaje de facturas está por encima del límite y el campo send_email_limit_invoice aún no se ha establecido en True
    # Cambiar el campo booleano en el modelo Seller solo la primera vez que supere el 90%
    if calculo_porciento >= 90 and not email_sent:
        seller.send_email_limit_invoice = True
        notification = SellerNotification(seller)
        notification.max_invoice_limit_notification()
    elif calculo_porciento < 90 and email_sent:
        seller.send_email_limit_invoice = False

    seller._skip_seller_signal = True
    seller.save()

def process_excel_files_and_update_product_data(instance, excel_fields, current_product_data):
    """
    Procesa los archivos Excel asociados a un objeto y actualiza el campo product_data.

    :param instance: Instancia del modelo que contiene los campos Excel.
    :param excel_fields: Diccionario donde las claves son nombres de tiendas y los valores son campos FileField.
    :param current_product_data: JSON actual del campo product_data.
    :return: JSON actualizado de product_data.
    """
    for store, excel_file in excel_fields.items():
        if excel_file:
            try:
                # Leer el archivo Excel
                df = pd.read_excel(excel_file)
                if df.empty:
                    continue  # Saltar archivos vacíos

                # Extraer IDs y calcular ventas
                product_ids = df.iloc[:, 0].tolist()
                sales_data = {}

                # Preparar todos los IDs para procesamiento
                processed_ids = []
                for product_id in product_ids:
                    # Normalizar el ID: asegurar que siempre tenga prefijo según formato actual
                    if isinstance(product_id, str) and product_id.startswith(f"{store[:3].upper()}-"):
                        prefixed_id = product_id
                    else:
                        prefixed_id = f"{store[:3].upper()}-{product_id}"

                    processed_ids.append(prefixed_id)
                    sales_data[prefixed_id] = sales_data.get(prefixed_id, 0) + 1

                # Consultar todos los productos de una vez para evitar múltiples consultas a DB
                # Solo consultamos los productos que necesitamos (los del archivo actual)
                store_products = {
                    prod.code: prod.description
                    for prod in StoreProduct.objects.filter(
                        code__in=processed_ids,
                        shop=store
                    )
                }

                # Validar y agregar datos al JSON de product_data
                if store not in current_product_data:
                    current_product_data[store] = []

                existing_ids = {p['id'] for p in current_product_data[store]}  # IDs ya existentes
                for product_id, ventas in sales_data.items():
                    # Obtener nombre del producto del cache creado
                    product_name = store_products.get(product_id)

                    # Asignar valor por defecto si no se encuentra el producto
                    if not product_name:
                        product_name = f"Producto {product_id} -- "

                    if product_id in existing_ids:
                        # Actualizar ventas del producto existente
                        for existing_product in current_product_data[store]:
                            if existing_product['id'] == product_id:
                                existing_product['ventas'] += ventas
                                break
                    else:
                        # Agregar nuevo producto si no existe en los datos actuales
                        current_product_data[store].append({
                            'id': product_id,
                            'name': product_name,
                            'ventas': ventas
                        })

            except Exception as e:
                raise ValidationError(f"Error procesando el archivo para la tienda '{store}': {str(e)}")

            # Limpiar el campo del archivo una vez procesado
            setattr(instance, f"{store}_excel", None)

    return current_product_data

def generate_product_data_table(product_data):
    """
    Genera una tabla HTML para mostrar los datos de productos.
    :param product_data: JSON con los datos de productos agrupados por tienda.
    :return: HTML seguro para renderizar en el admin.
    """
    if product_data:
        html_content = '<div style="background-color: black; color: white; padding: 10px; border-radius: 5px; max-width: 100%; overflow-x: auto;">'
        for store, products in product_data.items():
            html_content += f'<h3 style="color: white; text-align: center;">{store}</h3>'
            html_content += '''
            <table style="width: 100%; border-collapse: collapse; margin: 10px 0;">
                <thead>
                    <tr style="background-color: #333; color: white;">
                        <th style="padding: 8px; border: 1px solid #555;">ID Producto</th>
                        <th style="padding: 8px; border: 1px solid #555;">Nombre</th>
                        <th style="padding: 8px; border: 1px solid #555;">Cantidad</th>
                    </tr>
                </thead>
                <tbody>
            '''
            for product in products:
                html_content += f'''
                <tr>
                    <td style="padding: 8px; border: 1px solid #555; text-align: center;">{product['id']}</td>
                    <td style="padding: 8px; border: 1px solid #555; text-align: left;">{product['name']}</td>
                    <td style="padding: 8px; border: 1px solid #555; text-align: center;">{product['ventas']}</td>
                </tr>
                '''
            html_content += '</tbody></table>'
        html_content += '</div>'
        return format_html(html_content)

    return "No hay datos disponibles"

# Funciones utilitarias de Formularios IVA
def generate_document_field_mapping(iso_country_codes=None):
    debug and print("generate_document_field_mapping ejecutado con:", iso_country_codes)

    # 🔹 Documentos de información general
    company_info_documents = {
        "business_registry": "DOC-BusinessRegistry",
        "business_deeds": "DOC-BusinessDeeds",
        "amazon_vies_screenshot": "DOC-AmazonViesScreenshot",
        "comparison_certificate": "DOC-Comparisoncertificate",
        "mercantile_registry": "DOC-MercantileRegistry",
    }

    # 🔹 Documentos de migración
    migration_documents = {
        "france_old_manager_letter": "LETTER_manager_FR",
    }

    # 🔹 Documentos por país (automatizados)
    country_documents = {}

    if iso_country_codes:
        for iso in iso_country_codes:
            doc_code = f"{iso}-CURRENTVATDOCS"

            # Caso especial: Alemania (dos campos para el mismo tipo)
            if iso == "DE":
                country_documents["DE_CURRENTVATDOCS_1"] = f"{iso}-CURRENTVATDOCS-1"
                country_documents["DE_CURRENTVATDOCS_2"] = f"{iso}-CURRENTVATDOCS-2"
            else:
                field_name = f"{iso}_CURRENTVATDOCS"
                country_documents[field_name] = doc_code

    # 🔹 Documentos adicionales específicos por país
    extras = {
        "GB_PROOFOFADDRESS_1": "GB-PROOFOFADDRESS-1",
        "GB_PROOFOFADDRESS_2": "GB-PROOFOFADDRESS-2",
        "DE_BANKCERTIFICATE": "DE-BANKCERTIFICATE",
        "DE_MERCHANTTOKEN": "DE-MERCHANTTOKEN",
        "DE_VATSTATUS": "DE-VATSTATUS",
        "DE_FISCALRESIDENCE": "DE-FISCALRESIDENCE",
        "DE_FISCALIDCARD": "DE-FISCALIDCARD",
        "AE_SALESINVOICES_1": "AE-SALESINVOICES-1",
        "AE_SALESINVOICES_2": "AE-SALESINVOICES-2",
        "AE_SALESINVOICES_3": "AE-SALESINVOICES-3",
        "AE_SALESINVOICES_4": "AE-SALESINVOICES-4",
        "AE_SALESINVOICES_5": "AE-SALESINVOICES-5",
    }

    country_documents.update(extras)

    # 🔹 Mapeo combinado
    full_mapping = {}
    full_mapping.update(company_info_documents)
    full_mapping.update(migration_documents)
    full_mapping.update(country_documents)

    return {
        "full_mapping": full_mapping,
        "company_info": company_info_documents,
        "migration": migration_documents,
        "country_documents": country_documents,
    }

def get_or_create_document_type(code: str, country_name: str, suffix: str = "") -> dict:
    description = f"Documentos actuales del IVA ({country_name}){suffix}"
    obj, created = DocumentType.objects.get_or_create(
        code=code,
        defaults={"description": description}
    )

    try:
        if os.path.exists(DOCUMENT_TYPE_JSON_PATH):
            with open(DOCUMENT_TYPE_JSON_PATH, "r", encoding="utf-8") as f:
                data = json.load(f)
        else:
            os.makedirs(os.path.dirname(DOCUMENT_TYPE_JSON_PATH), exist_ok=True)
            data = []

        if isinstance(data, list):
            if not any(entry.get("code") == code for entry in data):
                data.append({"code": code, "description": description})
                with open(DOCUMENT_TYPE_JSON_PATH, "w", encoding="utf-8") as f:
                    json.dump(data, f, indent=4, ensure_ascii=False)
        else:
            debug and print("El archivo document_type.json no es una lista")

    except Exception as e:
        debug and print(f"Error al actualizar document_type.json: {e}")

    return {"code": code, "description": description, "created": created}

def get_documents_block_data(doc_mapping, seller, seller_vat=None):
    """
    Dado un mapeo {campo: código_documento}, retorna un dict con los datos de los documentos existentes.
    Siempre incluye todos los campos, incluso vacíos.
    """
    from muaytax.app_documents.models.document import Document

    result = {}

    for field_name, doc_code in doc_mapping.items():
        filters = {"seller": seller, "documentType__code": doc_code}
        if seller_vat:
            filters["sellerVat"] = seller_vat

        doc = Document.objects.filter(**filters).first()
        if doc and doc.file:
            result[field_name] = {
                "value": doc.file.name.split("/")[-1],
                "file": doc.file.url,
            }
        else:
            result[field_name] = {"value": "", "file": ""}  # Siempre incluir clave

    return result

def get_visible_fields_by_block(json_form, iso_country_codes=None):
    """
    Retorna un diccionario con los campos efectivamente visibles en cada bloque del formulario,
    extraídos desde el json_form. Se puede usar directamente para el sistema de validación del gestor.
    """
    if not isinstance(json_form, dict):
        return {}

    visible_fields = {}

    # Obtener el mapeo completo de documentos
    doc_mappings = generate_document_field_mapping(iso_country_codes or [])
    full_document_mapping = doc_mappings["full_mapping"]

    for block_key, block_data in json_form.items():
        if isinstance(block_data, dict):
            visible_fields[block_key] = []

            for field_key, field_val in block_data.items():
                # Validar si el campo es válido o parte de los campos mapeados
                if isinstance(field_val, (str, dict, int, float, bool)) and (
                    field_key in full_document_mapping or not block_key.startswith("documents_by_country_")
                ):
                    visible_fields[block_key].append(field_key)

    return visible_fields

def get_base_field_labels():
    field_list = [
        # Campos generales
        ("name", "Nombre"),
        ("legal_entity", "Tipo de Entidad"),
        ("seller_address", "Dirección"),
        ("phone", "Teléfono"),
        ("activity_type", "Tipo de Actividad"),
        ("desc_main_activity", "Descripción de la actividad principal"),
        ("products_and_services", "Producto o Servicio principal"),
        ("mercantile_registry", "Certificado Mercantil"),
        ("business_deeds", "Escrituras de Constitución"),
        ("business_registry", "Registro Comercial"),
        ("amazon_vies_screenshot", "Captura de Amazon VIES"),
        ("comparison_certificate", "Certificado Comparativo"),
        # Campos de migración
        ("previous_manager_end_date", "Fecha de baja con el gestor anterior"),
        ("previous_accounting_filed", "¿Contabilidad del año anterior presentada?"),
        ("current_accounting_filed_date", "Fecha hasta la que se presentó contabilidad actual"),
        ("vat_frequency", "Frecuencia de presentación del IVA"),
        ("previous_manager_name", "Nombre del antiguo gestor"),
        ("address", "Dirección del gestor anterior"),
        ("address_city", "Ciudad"),
        ("address_state", "Provincia"),
        ("address_zip", "Código postal"),
        ("address_country", "País"),
        ("france_old_manager_letter", "Carta de desvinculación (Francia)"),
    ]
    return {k: v for k, v in field_list}

def set_document_file_info(field_content, seller, seller_vat, doc_type):
    """ Asigna la URL y nombre del archivo si existe un documento previo coincidente, o limpia los valores si no existe """
    value = field_content.get("value", "")
    if value:
        try:
            doc = Document.objects.filter(
                seller=seller,
                sellerVat=seller_vat,
                documentType=doc_type,
                file__icontains=value
            ).first()
            if doc:
                field_content["file"] = doc.file.url
                field_content["value"] = doc.file.name.split("/")[-1]
                debug and print(f"📂 Documento recuperado de DB: {field_content['value']} → {field_content['file']}")
                return
        except Exception as e:
            debug and print(f"⚠️ Error buscando doc: {e}")
    field_content["file"] = ""
    field_content["value"] = ""
    debug and print("📂 Documento no encontrado, se dejan campos vacíos")

def send_payment_date_change_email(seller, service_name, field_name, previous_date, new_date, country=None):
    """
    Envía notificación cuando se actualiza o elimina una fecha de inicio de cobro

    Args:
        seller: Instancia del Seller
        service_name: Nombre del servicio (ej: "Contabilidad ES")
        field_name: Nombre del campo actualizado (ej: "contracted_accounting_payment_date")
        previous_date: Fecha anterior (puede ser None)
        new_date: Nueva fecha (None indica eliminación de la fecha de pago)
        country: País IVA si aplica (para servicios externos)
    """
    try:
        subject = 'MUAYTAX - Cambio en Fecha de Inicio de Cobro'
        from_email = '<EMAIL>'
        to_email = ['<EMAIL>']

        if not settings.IS_PRODUCTION:
            to_email = ['<EMAIL>']

        # Diccionario de equivalencias legal_entity
        legal_entity_map = {
            "self-employed": "Autónomo",
            "sl": "SL",
            "llc": "LLC",
            "other": "Otro",
        }

        # Obtener información básica del seller
        legal_entity = seller.legal_entity if hasattr(seller, 'legal_entity') else ''
        legal_entity_display = legal_entity_map.get(legal_entity, legal_entity)

        # Usar el nombre completo si está disponible, si no, el shortname
        full_name = f"{seller.first_name} {seller.last_name}" if seller.first_name and seller.last_name else seller.shortname

        # Preparar el mensaje de texto plano
        # Convertir fechas a objetos date si son strings
        if isinstance(previous_date, str):
            previous_date = datetime.strptime(previous_date, '%Y-%m-%d').date()
        if isinstance(new_date, str):
            new_date = datetime.strptime(new_date, '%Y-%m-%d').date()

        # Manejar caso de eliminación de fecha de pago
        if new_date is None:
            change_description = f"eliminada (anteriormente: {previous_date.strftime('%d/%m/%Y')})"
            action_type = "eliminada"
        elif previous_date:
            change_description = f"de {previous_date.strftime('%d/%m/%Y')} a {new_date.strftime('%d/%m/%Y')}"
            action_type = "actualizado"
        else:
            change_description = f"a {new_date.strftime('%d/%m/%Y')}"
            action_type = "establecido"

        country_info = f" ({country})" if country else ""

        # Ajustar mensaje según el tipo de acción
        if action_type == "eliminada":
            message_plain = (
                f"Se ha eliminado la fecha de inicio de cobro para el servicio '{service_name}{country_info}' "
                f"del cliente {full_name} con la empresa {seller} y entidad {legal_entity_display}.\n\n"
                f"La fecha de inicio de cobro ha sido {change_description}\n"
                f"Campo técnico: {field_name}\n\n"
                f"Este es un correo automático generado desde la plataforma."
            )
        else:
            message_plain = (
                f"Se ha {action_type} la fecha de inicio de cobro para el servicio '{service_name}{country_info}' "
                f"del cliente {full_name} con la empresa {seller} y entidad {legal_entity_display}.\n\n"
                f"Cambio realizado: {change_description}\n"
                f"Campo técnico: {field_name}\n\n"
                f"Este es un correo automático generado desde la plataforma."
            )

        # Preparar datos para el template
        template_data = {
            'seller_name': full_name,
            'seller_shortname': seller.shortname,
            'seller_email': seller.user.email,
            'legal_entity': legal_entity_display,
            'service_name': service_name,
            'field_name': field_name,
            'previous_date': previous_date,
            'new_date': new_date,
            'country': country,
            'action_type': action_type,
            'change_description': change_description,
            'logo_head_muaytax': logo_url_head_muaytax(),
        }

        # Renderizar el template con los datos preparados
        html_content = render_to_string('emails/payment_date_change_notification.html', template_data)

        # Configurar y enviar el correo
        bcc_email = ['<EMAIL>', '<EMAIL>']
        email = EmailMultiAlternatives(subject, message_plain, from_email, to_email, bcc=bcc_email)
        email.attach_alternative(html_content, "text/html")
        email.send()

        #debug and print(f'[PAYMENT_DATE_EMAIL] Email enviado correctamente para {service_name}')

    except Exception as e:
        debug and print(f'[PAYMENT_DATE_EMAIL] Error enviando email: {e}')
