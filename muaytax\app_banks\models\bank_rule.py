from django.db import models
from django.utils.translation import gettext_lazy as _

# Define choices outside the class
BANK_RULE_MATCH_TYPE_CHOICES = [
    ('equals', _('Igual que')),
    ('starts_with', _('Comienza con')),
    ('ends_with', _('Finaliza con')),
    ('contains', _('Contiene')),
]

class BankRule(models.Model):
    """
    Model for bank movement rules that can be applied to automatically match bank movements.
    """

    bank = models.ForeignKey(
        "banks.Bank",
        related_name="bank_rules",
        on_delete=models.CASCADE,
        verbose_name=_("Banco"),
    )

    rule_match_type = models.CharField(
        max_length=50,
        choices=BANK_RULE_MATCH_TYPE_CHOICES,
        default='equals',
        verbose_name=_("Tipo de Parámetro"),
    )

    pattern = models.CharField(
        max_length=255,
        verbose_name=_("Concepto"),
        help_text=_("Texto a buscar en el concepto del movimiento"),
    )

    is_active = models.BooleanField(
        default=True,
        verbose_name=_("Activa"),
    )

    priority = models.IntegerField(
        default=0,
        verbose_name=_("Prioridad"),
        help_text=_("Las reglas con mayor prioridad se aplican primero"),
    )

    seller = models.ForeignKey(
        "sellers.Seller",
        related_name="bank_rules",
        on_delete=models.CASCADE,
        verbose_name=_("Vendedor"),
    )

    accounting_account = models.ForeignKey(
        "dictionaries.AccountingAccount",
        related_name="bank_rules",
        on_delete=models.SET_NULL,
        verbose_name=_("Cuenta Contable"),
        null=True,
        blank=True,
    )

    created_at = models.DateTimeField(auto_now_add=True)
    modified_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _("Regla Bancaria")
        verbose_name_plural = _("Reglas Bancarias")
        ordering = ['-priority', 'bank__bank_name']

    def __str__(self):
        return f"{self.get_rule_match_type_display()} - {self.pattern} - {self.bank.bank_name}"