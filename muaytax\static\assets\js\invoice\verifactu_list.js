// ############# FUNCIONES GENERALES #############
const getValue = (elementId) => {
  const el = document.getElementById(elementId);
  return el ? el.value : null;
};

const ajaxData = (d) =>{
  const campos = {
    search: "search",
    status: "status",
    status_in_verifactu: "status_in_verifactu",
    operation_type: "operation_type",
    year: "year",
    period: "period"
  };

  for (const key in campos) {
    const value = getValue(campos[key]);
    if (value) {
      d[key] = value;
    }
  }
  return d;

}

const toggleApplyButton = (redraw=false)=>{
  let applyButton = document.getElementById("apply");
  let spinnerButton = document.getElementById("spinner-apply");
  if (!redraw) {
    (getComputedStyle(applyButton).display == "none") ? applyButton.style.display = "block" : applyButton.style.display = "none";
    (getComputedStyle(spinnerButton).display == "none") ? spinnerButton.style.display = "block" : spinnerButton.style.display = "none";
  }else{
    applyButton.style.display = "block";
    spinnerButton.style.display = "none";
  }
}

const enableDisabled = (enabled=true) =>{
  let selects = Array.from(document.getElementsByClassName("select-enabled"));
  let applyButton = document.getElementById("applyFiltersButton");
  let cleanButton = document.getElementById("cleanButton");

  !enabled ? applyButton.setAttribute("disabled", "disabled") : applyButton.removeAttribute("disabled");
  !enabled ? cleanButton.setAttribute("disabled", "disabled") : cleanButton.removeAttribute("disabled");

  selects.forEach(element => {
    if (!enabled) {
      element.classList.add("select-disabled");
    }else{
      element.classList.remove("select-disabled");
    }
  });
}

const disabledPeriod = () =>{
  let year = document.getElementById("year");
  let period = document.getElementById("period");


  if (year && year.value !='') {
    period.classList.remove("select-disabled");
  } else {
    period.classList.add("select-disabled");
    period.value = '';
  }
}

const updateInfoModal = (rowData)=>{
  $('#status_in_verifactu_modal').text(rowData.verifactu_status);
  $('#operation_type_modal').text(rowData.verifactu_operation_type);
  $('#expedition_date_modal').text(rowData.expedition_date);
  $('#verifactu_date_modal').text(rowData.verifactu_op_date);
  $('#inv_pk').val(rowData.id);

  let warning_block = $('#warning_info');
  let titleReg = $('#title_alredy_reg');
  let titleCancel = $('#title_cancel');
  let descReg = $('#desc_alredy_reg');
  let descCancel = $('#desc_cancel');
  let cancelBtn = $('#send_cancel_xml_verifactu');

  let is_already_sent = rowData.verifactu_status == 'Aceptada con errores' || rowData.verifactu_status == 'Correcto' ? true : false;
  let is_cancel = rowData.verifactu_operation_type ==  'Anulación' ? true : false;

  cancelBtn.attr('disabled', is_already_sent ? false : true);

  if(is_already_sent){
    warning_block.removeClass('d-none');
    if(is_cancel){
      titleCancel.removeClass('d-none');
      descCancel.removeClass('d-none');
      titleReg.addClass('d-none');
      descReg.addClass('d-none');
    }else{
      titleReg.removeClass('d-none');
      descReg.removeClass('d-none');
      titleCancel.addClass('d-none');
      descCancel.addClass('d-none');
    }
  }else{
    warning_block.addClass('d-none');
    titleReg.addClass('d-none');
    titleCancel.addClass('d-none');
    descReg.addClass('d-none');
    descCancel.addClass('d-none');
  }

}

const handleClick =(btn)=> {
  let rowData = JSON.parse(btn.getAttribute('data-json'));
  updateInfoModal(rowData);
  showModalVerifactu(rowData);
}

const showModalVerifactu = async (invoice) => {
  const container = document.getElementById('pdf_viewer_container');
  container.innerHTML = '';
  Swal.fire({
    title: gettext('Cargando datos de la factura'),
    text: gettext('Por favor, espere un momento.'),
    timerProgressBar: true,
    didOpen: () => Swal.showLoading(),
    showCancelButton: false,
    showConfirmButton: false,
    allowOutsideClick: false,
    allowEscapeKey: false,
    allowEnterKey: false
  });

  const timestamp = Date.now();
  const pdfUrl = `/sellers/${shortname}/invoice/${invoice.id}/file/?_=${timestamp}`;

  try {
    const response = await fetch(pdfUrl);
    if (!response.ok) throw new Error(gettext("No se pudo descargar el PDF"));

    const blob = await response.blob();
    const objectURL = URL.createObjectURL(blob);

    // Crear el <object> solo cuando tienes el PDF
    const obj = document.createElement('object');
    obj.setAttribute('type', 'application/pdf');
    obj.setAttribute('class', 'pdf border');
    obj.setAttribute('id', 'obj_pdf');
    obj.setAttribute('data', objectURL);
    obj.classList.add('pdf-object');

    const embed = document.createElement('embed');
    embed.setAttribute('type', 'application/pdf');
    embed.setAttribute('class', 'pdf border');
    embed.setAttribute('id', 'embed_pdf');
    embed.setAttribute('src', objectURL);

    const link = document.createElement('a');
    link.setAttribute('href', objectURL);
    link.setAttribute('download', '');
    link.innerText = gettext('Descargar');

    obj.appendChild(embed);
    obj.appendChild(link);
    container.appendChild(obj);

  } catch (error) {
    container.innerHTML = `<p class="text-danger">${gettext('Error al cargar el PDF')}</p>`;
    console.error(error);
  }
  Swal.close();
  $("#modalVerifactuOptions").modal("show");
};

const sendInvoiceVeriFactuToAEAT = async (shortname, invoice, action=null) => {


  $('#modalVerifactuOptions').modal('hide');

  // Show loading message
  Swal.fire({
    title: action == 'check_record_aeat'? gettext('Comprobando factura en Verifactu'): gettext('Enviando factura a Verifactu'),
    text: gettext('Por favor, espere un momento.'),
    timerProgressBar: true,
    didOpen: () => Swal.showLoading(),
    showCancelButton: false,
    showConfirmButton: false,
    allowOutsideClick: false,
    allowEscapeKey: false,
    allowEnterKey: false
  });

  try {
    let url = `/sellers/${shortname}/invoices/sendXMLVeriFactuAEAT/${invoice}`;

    let bodyData = { action };

    let response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRFToken': document.querySelector('input[name="csrfmiddlewaretoken"]')?.value || '',
      },
      body: JSON.stringify(bodyData),
    });

    let result = await response.json();
    if (response.status == 200) {
      Swal.fire({
        icon: 'success',
        title: gettext('Petición enviada correctamente a la AEAT'),
        width: 1200,
        html: formatJSONToHTML(result),
      });
      try{
        if (action != 'check_record_aeat'){
          if(result.RespuestaRegFactuSistemaFacturacion.RespuestaLinea.EstadoRegistro == 'Correcto'){
            dataTable.draw();
          }
        }
      }catch (error) {
        console.error('Error:', error);
      }
    }
    if (result.error){
      Swal.fire({
        icon: 'error',
        title: gettext('Error al enviar la petición a la AEAT'),
        width: 1200,
        html: formatJSONToHTML(result),
      });
    }
  } catch (error) {
      console.error('Error:', error);
      Toast.fire({
        time: 5000,
        icon: 'error',
        title: gettext('Error contacta con soporte.'),
      });
  }
};

const formatJSONToHTML = (obj, level = 0) => {
  let html = '<ul style="list-style-type: none; padding-left: 0;">';

  const flattenObject = (data, parentKey = '') => {
      for (const key in data) {
          if (typeof data[key] === 'object' && data[key] !== null) {
              html += `<li style=" text-align: left; margin-top: 10px; font-weight: bold; font-size: 16px;">${key.toUpperCase()}</li>`;
              html += '<ul style="list-style-type: none; padding-left: 15px;">';
              flattenObject(data[key], key);
              html += '</ul>';
          } else {
              html += `<li style="text-align: left; font-size: 14px;">•&nbsp;<strong>${key}:</strong> ${data[key]}</li>`;
          }
      }
  };

  flattenObject(obj);
  html += '</ul>';
  return html;
}

const dataTableOptions = {
  paging: true,
  searching: true,
  ordering: true,
  truncation: true,
  info: true,
  footer: true,
  fixedHeader: true,
  serverSide: true,
  order: [[3, "desc"]],
  lengthMenu: [
    [50, 100, 200],
    [50, 100, 200],
  ],
  language: {
    lengthMenu: "_MENU_",
    zeroRecords: gettext("No se han encontrado facturas."),
    info: gettext("_TOTAL_ resultados. "),
    search: gettext("Buscar:"),
    infoEmpty: gettext("No hay resultados que coincidan con su búsqueda."),
    infoFiltered: "",
    paginate: {
      first: gettext("Primera"),
      last: gettext("Última"),
      next: gettext("Siguiente"),
      previous: gettext("Anterior"),
    },
  },
  dom: "lrtip",
  ajax: {
    url: `/sellers/${shortname}/invoices/veriFactuInvoiceListDT/`,
    type: "GET",
    data: function (d) {
      ajaxData(d);
    },
    dataSrc: function (json) {
      console.log("JSON: ", json);
      document.getElementById("total_verifactu_invoices").innerText =
        json.total_invoices_count;
      document.getElementById("correct_invoices_count").innerText =
        json.total_sent_count;
      document.getElementById("correct_with_errors_invoices_count").innerText =
        json.total_sent_with_errors_count;
      document.getElementById("total_not_sent").innerText = json.not_sent_count;

      return json.data;
    },
  },
  columns: [
    { data: "id" },
    {
      data: "status",
      orderable: true,
      render: function (data, type, row) {
        let html = "";
        if (row.status) {
          let bg = "";
          const matchingStatus = invoiceStatuses.find(
            (status) => status.fields.translated_description === row.status
          );
          if (matchingStatus) {
            switch (matchingStatus.fields.status_code) {
              case "pending":
              case "revision-pending":
                bg = "bg-warning";
                break;
              case "revised":
                bg = "bg-success";
                break;
              case "discard":
                bg = "bg-danger";
                break;
              default:
                bg = "bg-secondary";
            }
            html = `<span class="rounded ${bg} text-white p-1">
                          <b> &nbsp; ${matchingStatus.fields.translated_description} &nbsp; </b>
                          </span>`;
          } else {
            bg = "bg-secondary";
            html = `<span class="rounded ${bg} text-white p-1">&nbsp; ${row.status} &nbsp;</span>`;
          }
        }
        return html;
      },
    },
    { data: "reference" },
    { data: "expedition_date" },
    {
      data: "verifactu_status",
      orderable: true,
      render: function (data, type, row) {
        let html = `<div style="display: flex; justify-content: center; gap: 5px;">`;
        if (row.verifactu_status == "Correcto") {
          html += `<span data-bs-toggle="tooltip" data-bs-placement="top" title="${row.verifactu_status}" ><i class="fa-solid  fa-xl fa-check" style="color: #02c018;"></i></span>`;
        } else if (row.verifactu_status == "No enviada") {
          html += `<span data-bs-toggle="tooltip" data-bs-placement="top" title="${row.verifactu_status}" ><i class="fa-solid  fa-xl fa-xmark" style="color: #dc3545;"></i></span>`;
        } else if (row.verifactu_status == "Aceptada con errores") {
          html += `<span data-bs-toggle="tooltip" data-bs-placement="top" title="${row.verifactu_status}" >`;
          html += `<i class="fa-solid  fa-xl fa-check" style="color: #02c018;"></i>`;
          html += `<i class="fa-solid fa-xl fa-triangle-exclamation" style="color: #ffc107;" ></i>`;
          html += `</span>`;
        }
        html += `</div>`;
        return html;
      },
    },
    { data: "verifactu_operation_type" },
    { data: "verifactu_op_date" },
    {
      data: "is_manager_cancel_sending_to_verifactu",
      orderable: false,
      render: function (data, type, row) {
        return data == "True" ? gettext("Si") : gettext("No");
      },
    },
    {
      data: null,
      orderable: false,
      render: function (data, type, row) {
        let html = "";
        html += `<div style="display: flex; gap: 12px;">`;
        html += `<a class="btn btn-success btn-icon tooltip-wrapper tooltip-button" style="margin: 0px;" href="/sellers/${shortname}/invoice/${
          row.id
        }" data-bs-toggle="tooltip" data-bs-placement="top" data-bs-original-title="${gettext(
          "Categorizar Factura"
        )}">
                  <i class="fa-solid fa-pen-to-square"></i>
                </a>`;
        html += `
                      <div class="btn-group dropstart" data-bs-toggle="tooltip" data-bs-placement="top" title="${gettext(
                        "Descargas"
                      )}" style="margin-bottom: 2.5%;">
                        <button class="btn btn-icon btn-info dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="true">
                          <i class="fa-solid fa-download "></i>
                        </button>
                        <ul class="dropdown-menu">
                          <li>
                            <a href="/sellers/${shortname}/invoice/${
          row.id
        }/file/" target="_blank" download>
                              <i class="fa-regular fa-file-pdf"></i>
                              ${gettext("Descargar Factura")}
                            </a>
                          </li>
                          <li>
                            <a href="/sellers/${shortname}/invoices/createxmlVeriFactu/${
          row.id
        }" target="_blank" download>
                              <i class="fa-solid fa-file-arrow-down"></i>
                              ${gettext("Descargar XML Alta")}
                            </a>
                          </li>
                          <li>
                            <a href="/sellers/${shortname}/invoices/createxmlVeriFactu/${
          row.id
        }?action=check_record_aeat" target="_blank" download>
                              <i class="fa-solid fa-magnifying-glass"></i>
                              ${gettext("Descargar XML Comprobación")}
                            </a>
                          </li>
                          <li>
                            <a href="/sellers/${shortname}/invoices/createxmlVeriFactu/${
          row.id
        }?action=cancel_record_aeat" target="_blank" download>
                              <i class="fa-solid fa-ban"></i>
                              ${gettext("Descargar XML Cancelación")}
                            </a>
                          </li>
                          ${
                            row.verifactu_status == "Aceptada con errores" ||
                            row.verifactu_status == "Correcto"
                              ? `
                          <li>
                            <a href="/sellers/${shortname}/invoices/createxmlVeriFactu/${
                                  row.id
                                }?action=xml_from_aeat" target="_blank" download>
                              <i class="fa-regular fa-file-code"></i>
                              ${gettext("Descargar XML Respuesta de la AEAT")}
                            </a>
                          </li>`
                              : ""
                          }
                        </ul>
                      </div>
                      `;
        if (row.status == "Revisada") {
          html += `<button type="button"
                    class="btn"
                    style="background-color: white; padding: 5px; box-shadow: rgba(0, 0, 0, 0.1) 0px 0.04em 0.04em, rgba(0, 0, 0, 0.1) 0px 0.08em 0.25em, rgba(255, 255, 255, 0.03) 0px 0px 0px 1px inset; width: 35px; margin: 0;"
                    onclick="handleClick(this)"
                    data-json='${JSON.stringify(row)}'
                    id="verifactu-button"
                    >
                    <img data-bs-toggle="tooltip" data-bs-placement="top" data-bs-original-title="${gettext(
                      "Ver opciones Verifactu"
                    )}" src="/static/assets/images/logos/svg/other/aeat_logo.svg" alt="AEAT" class="rounded" />
                  </button>`;
          html += `</div>`;
        }
        return html;
      },
    },
  ],
  drawCallback: function (settings, json) {
    $('[data-bs-toggle="tooltip"]').tooltip();
    toggleApplyButton(true);
    enableDisabled();
    disabledPeriod();
  },
};

let dataTable =$("#list-table").DataTable(dataTableOptions);

const filter = () => {
  toggleApplyButton();
  enableDisabled(false);
  dataTable.draw();
}

const cleanFilters = () => {
  let selects = Array.from(document.getElementsByClassName("select-enabled"));
  selects.forEach(element => {
    element.value = '';
  })
  filter();
}

// ####### FUNCIONES QUE DEBEN EJECUTARSE AL CARGAR LA PAGINA ########
enableDisabled(false);

// ####### LISTENERS #######
document.getElementById("year").addEventListener("change", function() {
  disabledPeriod()
});

document.getElementById("search").addEventListener("keydown", function() {
  if (event.key === "Enter") {
      filter();
    }
});

document.getElementById("send_xmlVerifactu").addEventListener("click", async function () {
  let invoice = document.getElementById("inv_pk").value;
  try {
    await sendInvoiceVeriFactuToAEAT(shortname, invoice);
  } catch (error) {
    console.error(gettext("Error al enviar la factura:"), error);
  }
});

document.getElementById("send_check_xml_verifactu").addEventListener("click", async function () {
  let invoice = document.getElementById("inv_pk").value;
  try {
    await sendInvoiceVeriFactuToAEAT(shortname, invoice, "check_record_aeat" );
  } catch (error) {
    console.error(gettext("Error al enviar la factura:"), error);
  }
});

document.getElementById("send_cancel_xml_verifactu").addEventListener("click", async function () {
  let invoice = document.getElementById("inv_pk").value;
  try {
    await sendInvoiceVeriFactuToAEAT(shortname, invoice, "cancel_record_aeat" );
  } catch (error) {
    console.error(gettext("Error al enviar la factura:"), error);
  }
});


// Manejar el cambio de año y habilitar/deshabilitar el mes
// const onChangeYear = () => {
//   const year = document.getElementById("year");
//   const period = document.getElementById("multiple-month");
//   console.log("EL AÑO ES: ", year.value)
//   if (year && year.value) {
//     period.removeAttribute("disabled");  // Habilitar selector de meses
//   } else {
//     period.setAttribute("disabled", "disabled");  // Deshabilitar selector de meses
//     period.value = '';  // Limpia el valor del selector
//     // addFiltersBadge(period);
//   }
// }

// const toggleApplyFiltersButton=(state) =>{
//       const applyFiltersButton = document.getElementById("applyFiltersButton");
//       applyFiltersButton.disabled = !state;
// }

// const getFilterValues =()=> {
//       const form = document.getElementById("dropdownFiltersForm");
//       const multiSelectors = document.querySelectorAll('[separator]');
//       const selectInputs = form.querySelectorAll('select');
//       let filterDict = {};

//       selectInputs.forEach((select) => {
//         filterDict[select.id] = select.value;
//       });

//       multiSelectors.forEach((multiSelector) => {
//         if (multiSelector.dataset.value) {
//           filterDict[multiSelector.id] = multiSelector.dataset.value.split(multiSelector.getAttribute('separator'));
//         } else {
//           filterDict[multiSelector.id] = [];
//         }
//       });

//       return filterDict;
//     }

// const checkFilterState =()=>{

//       const currentFilter = getFilterValues();

//       // Comprueba si algún filtro tiene valor
//       const hasActiveFilters = Object.values(currentFilter).some(value => {
//         return Array.isArray(value) ? value.length > 0 : !!value;
//       });

//       toggleApplyFiltersButton(JSON.stringify(currentFilter));
//       console.log("Current filter: ", currentFilter);
//       console.log("Has active filters: ", hasActiveFilters);
//       console.log(JSON.stringify(currentFilter))
//       return JSON.stringify(currentFilter);
// }

// const clearFiltersBadge = () => {
//   Object.keys(filtersDictCount).forEach(key => filtersDictCount[key] = false);
//   const badge = document.getElementById('id-filter-notification');
//   badge.classList.add('d-none');
//   badge.innerHTML = '';
// }

// Restablecer todos los filtros
// const resetFilters =()=> {

//   const form = document.getElementById("dropdownFiltersForm");
//   const multiSelectors = document.querySelectorAll('[separator]');
//   const selectInputs = form.querySelectorAll('select');

//   // Restablecer los valores de los selects
//   selectInputs.forEach((select) => {
//     select.value = '';
//     console.log(`Reseteando select #${select.id} - valor actual: ${select.value}`);
//   });

//   // Restablecer los valores de los multi-selectores
//   multiSelectors.forEach((multiSelector) => {
//     let listenerCallback = null;

//     multiSelector.dataset.value = ''; // Resetear dataset
//     multiSelector.value = '';

//     if (typeof multiSelector.updateItems === 'function') {
//       multiSelector.updateItems();
//     }

//     // Sincronizar visualmente el estado de los checkboxes
//     const shadowRoot = multiSelector.shadowRoot || multiSelector;
//     const allCheckboxes = shadowRoot.querySelectorAll('input[type="checkbox"]');
//     const selectAllInput = shadowRoot.querySelector('#select-all');

//     // Restablecer "Select All" y checkboxes
//     allCheckboxes.forEach((checkbox) => {
//       checkbox.checked = false;
//     });

//     if (selectAllInput) {
//       selectAllInput.checked = false;
//       console.log(`"Select All" reseteado en #${multiSelector.id}`);
//     }

//     // Forzar actualización del dataset y del estado
//     multiSelector.setAttribute('value', '');
//     multiSelector.setAttribute('data-value', '');

//     // check if multicheck is "multiple-taxcountries" to call onTaxCountryChange
//     if (multiSelector.id === 'multiple-taxcountries') {
//       listenerCallback = onTaxCountryChange;
//     }

//     addListenerToMultiSelector(multiSelector, listenerCallback);

//   });

//   // Llamada a funciones dependientes
//   onTaxCountryChange();
//   onChangeYear();

//   // Actualizar los filtros y el estado del botón
//   //filter();
//   clearFiltersBadge();
//   toggleApplyFiltersButton(false);


//   dataTable.on('draw', function () {
//     const dropdownFiltersForm = document.getElementById('dropdownFiltersForm');
//     dropdownFiltersForm.style.display = 'none';
//   });
// }

// //LÓGICA DEL BOTÓN DEL FILTRO DESPLEGABLE
// const dropdownButton = document.getElementById('dropdownButton');
// const dropdownFiltersForm = document.getElementById('dropdownFiltersForm');

// dropdownButton.addEventListener('click', function (event) {
//   dropdownFiltersForm.style.display = dropdownFiltersForm.style.display === 'block' ? 'none' : 'block';
//   if (dropdownFiltersForm.style.display === 'block') {
//     dropdownFiltersForm.scrollIntoView({behavior: "smooth", block: "center", inline: "center"});
//   }
// });

// document.addEventListener('click', function (event) {
//   if (!dropdownFiltersForm.contains(event.target) && !dropdownButton.contains(event.target)) {
//     dropdownFiltersForm.style.display = 'none';
//   }
// });

// dropdownFiltersForm.addEventListener('click', function (event) {
//   hideMultiDropDown(event);
//   event.stopPropagation();

// });

// const hideMultiDropDown = (event)=> {
//   const multiSelectorElements = document.querySelectorAll('[separator]');

//   multiSelectorElements.forEach((element) => {
//     if (!event.composedPath().includes(element) && typeof element.hideDropDown === 'function') {
//       element.dropDownVisible = false;
//       element.hideDropDown();
//     }
//   });
// }


// const dropdownFilterFormHack = () => {
//   const dropdownFiltersForm = document.getElementById('dropdownFiltersForm');
//   dropdownFiltersForm.style.opacity = '1';
//   dropdownFiltersForm.style.display = 'none';
// }

// onChangeYear()
// dropdownFilterFormHack()
// toggleApplyFiltersButton(false);

